/* Reset và Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1e3c72 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #333;
    overflow: hidden;
}

/* Game Container */
.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    text-align: center;
    max-width: 900px;
    width: 95%;
}

/* Header */
.header {
    margin-bottom: 20px;
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 15px;
    color: #2d3748;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.mission-info {
    display: flex;
    justify-content: space-between;
    background: #f7fafc;
    padding: 15px;
    border-radius: 10px;
    border: 2px solid #e2e8f0;
    flex-wrap: wrap;
    gap: 10px;
}

.mission, .score {
    font-size: 1.1em;
    font-weight: bold;
    color: #2d3748;
    flex: 1;
    min-width: 200px;
}

.mission span:last-child {
    color: #3182ce;
}

.score span:last-child {
    color: #38a169;
}

/* Game Area */
.game-area {
    position: relative;
    margin: 20px 0;
    display: flex;
    justify-content: center;
}

#gameCanvas {
    border: 3px solid #2d3748;
    border-radius: 10px;
    background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 100%);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

/* HUD Overlay */
.hud {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 10px;
    pointer-events: none;
    z-index: 5;
}

.instrument {
    background: rgba(0, 0, 0, 0.8);
    color: #00ff00;
    padding: 10px;
    border-radius: 8px;
    border: 2px solid #00ff00;
    min-width: 120px;
    font-family: 'Courier New', monospace;
    text-align: center;
}

.instrument .label {
    font-size: 0.8em;
    margin-bottom: 5px;
    color: #ffffff;
}

.instrument .value {
    font-size: 1.4em;
    font-weight: bold;
    margin-bottom: 2px;
}

.instrument .unit {
    font-size: 0.7em;
    color: #cccccc;
}

.fuel-bar {
    width: 100%;
    height: 8px;
    background: #333;
    border-radius: 4px;
    margin: 5px 0;
    overflow: hidden;
}

.fuel-fill {
    height: 100%;
    background: linear-gradient(to right, #ff0000 0%, #ffff00 50%, #00ff00 100%);
    width: 100%;
    transition: width 0.3s ease;
}

/* Overlays */
.overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.85);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    z-index: 10;
}

.overlay.hidden {
    display: none;
}

.overlay-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    max-height: 80vh;
    overflow-y: auto;
}

.overlay-content h2 {
    margin-bottom: 15px;
    color: #2d3748;
    font-size: 1.8em;
}

.overlay-content p {
    margin-bottom: 10px;
    color: #4a5568;
    line-height: 1.5;
}

.controls-info, .mission-brief {
    background: #f7fafc;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    border: 1px solid #e2e8f0;
    text-align: left;
}

.controls-info h3, .mission-brief h3 {
    color: #2d3748;
    margin-bottom: 10px;
    text-align: center;
}

.controls-info p, .mission-brief p {
    margin: 5px 0;
    font-size: 0.9em;
}

/* Buttons */
.btn {
    background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 5px;
    box-shadow: 0 4px 15px rgba(49, 130, 206, 0.4);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(49, 130, 206, 0.6);
}

.btn:active {
    transform: translateY(0);
}

.btn.secondary {
    background: linear-gradient(135deg, #718096 0%, #4a5568 100%);
    box-shadow: 0 4px 15px rgba(113, 128, 150, 0.4);
}

.btn.secondary:hover {
    box-shadow: 0 6px 20px rgba(113, 128, 150, 0.6);
}

/* Controls */
.controls {
    margin: 20px 0;
}

.desktop-controls {
    background: #f7fafc;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 15px;
    border: 2px solid #e2e8f0;
}

.desktop-controls h3 {
    color: #2d3748;
    margin-bottom: 15px;
}

.control-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    text-align: left;
}

.control-group h4 {
    color: #3182ce;
    margin-bottom: 8px;
}

.control-group p {
    margin: 3px 0;
    color: #4a5568;
    font-size: 0.9em;
}

.mobile-controls {
    display: grid;
    gap: 10px;
    max-width: 300px;
    margin: 0 auto;
}

.control-row {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.control-btn {
    width: 60px;
    height: 60px;
    border: 2px solid #3182ce;
    background: rgba(49, 130, 206, 0.1);
    border-radius: 15px;
    font-size: 1.5em;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #3182ce;
    font-weight: bold;
}

.control-btn:hover {
    background: rgba(49, 130, 206, 0.2);
    transform: scale(1.05);
}

.control-btn:active {
    background: rgba(49, 130, 206, 0.3);
    transform: scale(0.95);
}

/* Tips */
.tips {
    background: #f7fafc;
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #e2e8f0;
    margin-top: 20px;
}

.tips h3 {
    color: #2d3748;
    margin-bottom: 15px;
}

.tips ul {
    text-align: left;
    color: #4a5568;
    line-height: 1.6;
}

.tips li {
    margin: 8px 0;
}

.tips strong {
    color: #3182ce;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-container {
        width: 98%;
        padding: 15px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .mission-info {
        flex-direction: column;
        text-align: center;
    }
    
    #gameCanvas {
        width: 100%;
        max-width: 400px;
        height: auto;
    }
    
    .hud {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .instrument {
        min-width: 100px;
        font-size: 0.8em;
    }
    
    .overlay-content {
        padding: 20px;
        max-width: 320px;
    }
    
    .control-grid {
        grid-template-columns: 1fr;
    }
    
    .mobile-controls {
        display: grid;
    }
    
    .desktop-controls {
        display: none;
    }
}

@media (min-width: 769px) {
    .mobile-controls {
        display: none;
    }
}

/* Animations */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.btn:focus {
    outline: none;
    animation: pulse 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.game-container.crash {
    animation: shake 0.5s ease-in-out;
}

/* Status Colors */
.instrument.warning .value {
    color: #ffa500;
}

.instrument.danger .value {
    color: #ff0000;
}

.instrument.success .value {
    color: #00ff00;
}
