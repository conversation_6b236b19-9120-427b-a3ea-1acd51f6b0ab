@echo off
echo 🚕 Taxi Simulator Launcher
echo ==========================
echo.
echo Checking dependencies...
python -c "import pygame; print('✅ Pygame OK!')" 2>nul
if errorlevel 1 (
    echo ❌ Pygame missing!
    echo Installing pygame...
    pip install pygame
    if errorlevel 1 (
        echo ❌ Failed to install pygame!
        pause
        exit /b 1
    )
)

echo.
echo 🚀 Starting Taxi Simulator...
echo.
echo Welcome to City Taxi Driver!
echo.
echo Controls:
echo   WASD - Drive taxi
echo   Space - Pick up/Drop off passenger  
echo   R - Refuel
echo   M - Toggle meter
echo   ESC - Pause/Menu
echo.
echo Your goal: Pick up passengers and earn money!
echo Manage fuel and reputation to become the best taxi driver!
echo.
echo Press any key to start...
pause >nul

python main.py

echo.
echo Thanks for playing Taxi Simulator!
pause
