#!/usr/bin/env python3
"""
Simple test for Flight Simulator 3D
"""

try:
    import pygame
    import math
    import numpy as np
    print("✅ All dependencies imported successfully!")
    
    # Initialize Pygame
    pygame.init()
    print("✅ Pygame initialized!")
    
    # Create a simple window
    screen = pygame.display.set_mode((800, 600))
    pygame.display.set_caption("Flight Simulator 3D - Test")
    print("✅ Window created!")
    
    # Simple test loop
    clock = pygame.time.Clock()
    running = True
    frame_count = 0
    
    print("🚀 Starting test loop... (will run for 3 seconds)")
    
    while running and frame_count < 180:  # 3 seconds at 60 FPS
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
        
        # Fill screen with blue (sky)
        screen.fill((135, 206, 235))
        
        # Draw a simple aircraft
        aircraft_x = 400 + math.sin(frame_count * 0.1) * 100
        aircraft_y = 300 + math.cos(frame_count * 0.05) * 50
        
        pygame.draw.polygon(screen, (255, 0, 0), [
            (aircraft_x, aircraft_y),
            (aircraft_x - 10, aircraft_y + 20),
            (aircraft_x + 10, aircraft_y + 20)
        ])
        
        # Draw some text
        font = pygame.font.Font(None, 36)
        text = font.render("Flight Simulator 3D Test", True, (255, 255, 255))
        screen.blit(text, (250, 50))
        
        info_text = font.render(f"Frame: {frame_count}", True, (255, 255, 255))
        screen.blit(info_text, (50, 100))
        
        pygame.display.flip()
        clock.tick(60)
        frame_count += 1
    
    print("✅ Test completed successfully!")
    print("🎉 Flight Simulator 3D is ready to run!")
    
    pygame.quit()
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install dependencies: pip install pygame numpy")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
