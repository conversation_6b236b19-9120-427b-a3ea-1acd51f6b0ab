# 🏎️ Racing Game

Một game đua xe 2D thú vị được viết bằng HTML5, CSS3 và JavaScript.

## 🎮 Cách chơi

### Mục tiêu
- Điều khiển xe đỏ tránh các xe khác trên đường
- <PERSON><PERSON> được càng xa càng tốt để ghi điểm cao
- Tốc độ sẽ tăng dần theo thời gian

### Điều khiển

#### 🖥️ Desktop
- **A** hoặc **←**: Rẽ trái
- **D** hoặc **→**: Rẽ phải  
- **Space**: Tạm dừng/Tiếp tục

#### 📱 Mobile
- **Chạm trái/phải**: <PERSON> chuyển xe
- **Vuốt trái/phải**: <PERSON> chuyển xe
- **Nút điều khiển**: Sử dụng các nút trên màn hình

## ✨ Tính năng

### 🎯 Gameplay
- **3 làn đường**: Di chuyển linh hoạt giữa các làn
- **Xe AI thông minh**: <PERSON><PERSON><PERSON> xe khác xuất hiện ngẫu nhiên
- **Tăng độ khó**: Tốc độ tăng dần theo điểm số
- **Hệ thống va chạm**: Phát hiện va chạm chính xác

### 🎨 Đồ họa
- **Đường 3D**: Hiệu ứng đường chạy mượt mà
- **Xe đa dạng**: 5 loại xe khác nhau với màu sắc khác nhau
- **Cảnh quan**: Cây cối và cỏ xanh hai bên đường
- **Hiệu ứng**: Animation mượt mà 60fps

### 🔊 Âm thanh
- **Âm thanh va chạm**: Hiệu ứng crash khi tai nạn
- **Web Audio API**: Âm thanh được tạo động

### 💾 Lưu trữ
- **High Score**: Lưu kỷ lục trong localStorage
- **Thống kê**: Theo dõi điểm số và tốc độ tối đa

### 📱 Responsive
- **Desktop**: Tối ưu cho màn hình lớn
- **Mobile**: Hỗ trợ touch controls
- **Tablet**: Adaptive layout

## 🚀 Cách chạy

1. Mở terminal trong thư mục `racing-game`
2. Chạy server local:
   ```bash
   python -m http.server 8000
   ```
3. Mở browser và truy cập: `http://localhost:8000`

## 🏆 Hệ thống điểm

- **Điểm số**: Tăng theo khoảng cách đi được
- **Tốc độ**: Hiển thị theo km/h
- **Kỷ lục**: Lưu điểm cao nhất
- **Thành tích mới**: Thông báo khi phá kỷ lục

## 💡 Mẹo chơi

1. **Giữ xe ở giữa**: Dễ dàng né tránh khi cần
2. **Quan sát trước**: Nhìn xa để chuẩn bị di chuyển
3. **Không vội vàng**: Di chuyển từ từ, chính xác
4. **Tập trung**: Tốc độ cao cần phản xử nhanh

## 🛠️ Công nghệ sử dụng

- **HTML5 Canvas**: Rendering đồ họa 2D
- **CSS3**: Styling và animations
- **JavaScript ES6**: Game logic và controls
- **Web Audio API**: Tạo âm thanh động
- **LocalStorage**: Lưu trữ dữ liệu

## 📁 Cấu trúc file

```
racing-game/
├── index.html      # Cấu trúc HTML
├── style.css       # Styling và responsive
├── script.js       # Game logic
└── README.md       # Tài liệu này
```

## 🎯 Tính năng có thể mở rộng

- [ ] Nhiều loại đường (cua, dốc)
- [ ] Power-ups (tăng tốc, bất tử)
- [ ] Nhiều loại xe player
- [ ] Multiplayer mode
- [ ] Leaderboard online
- [ ] Âm nhạc nền
- [ ] Hiệu ứng hạt (particle effects)

---

**Chúc bạn chơi vui vẻ! 🏁**
