@echo off
echo ✈️ Flight Simulator 3D Launcher
echo ================================
echo.
echo Checking dependencies...
python -c "import pygame, numpy; print('✅ Dependencies OK!')" 2>nul
if errorlevel 1 (
    echo ❌ Dependencies missing!
    echo Installing pygame and numpy...
    pip install pygame numpy
    if errorlevel 1 (
        echo ❌ Failed to install dependencies!
        pause
        exit /b 1
    )
)

echo.
echo 🚀 Starting Flight Simulator 3D...
echo.
echo Controls:
echo   WASD - Pitch/Yaw
echo   QE - Roll  
echo   Space - Throttle up
echo   Shift - Throttle down
echo   I - Start/Stop engine
echo   C - Change camera
echo   ESC - Quit
echo.
echo Press any key to start...
pause >nul

python main.py

echo.
echo Flight Simulator 3D closed.
pause
