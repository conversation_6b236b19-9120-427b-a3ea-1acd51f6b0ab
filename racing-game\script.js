// Racing Game - Complete Implementation
class RacingGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');

        // Game settings
        this.roadWidth = 300;
        this.roadX = (this.canvas.width - this.roadWidth) / 2;
        this.laneWidth = this.roadWidth / 3;

        // Player car
        this.player = {
            x: this.canvas.width / 2 - 15,
            y: this.canvas.height - 80,
            width: 30,
            height: 60,
            speed: 0,
            lane: 1 // 0=left, 1=center, 2=right
        };

        // Game state
        this.gameRunning = false;
        this.gamePaused = false;
        this.score = 0;
        this.speed = 0;
        this.maxSpeed = 0;
        this.highScore = localStorage.getItem('racingHighScore') || 0;

        // Traffic cars
        this.cars = [];
        this.carSpawnTimer = 0;
        this.carSpawnRate = 120; // frames between cars

        // Road animation
        this.roadOffset = 0;
        this.roadSpeed = 2;

        // Controls
        this.keys = {};
        this.touchStartX = 0;

        this.init();
    }

    init() {
        this.updateHighScoreDisplay();
        this.setupEventListeners();
        this.showStartScreen();
        this.drawInitialScene();
    }

    setupEventListeners() {
        // Keyboard controls
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));

        // Button controls
        document.getElementById('start-btn').addEventListener('click', () => this.startGame());
        document.getElementById('restart-btn').addEventListener('click', () => this.restartGame());
        document.getElementById('resume-btn').addEventListener('click', () => this.resumeGame());
        document.getElementById('restart-pause-btn').addEventListener('click', () => this.restartGame());

        // Mobile controls
        document.getElementById('left-btn').addEventListener('click', () => this.moveLeft());
        document.getElementById('right-btn').addEventListener('click', () => this.moveRight());
        document.getElementById('pause-btn').addEventListener('click', () => this.togglePause());

        // Touch controls
        this.canvas.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: false });
        this.canvas.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: false });

        // Prevent scrolling
        this.canvas.addEventListener('touchstart', (e) => e.preventDefault());
        this.canvas.addEventListener('touchmove', (e) => e.preventDefault());
    }

    handleKeyDown(e) {
        this.keys[e.key] = true;

        if (e.key === ' ') {
            e.preventDefault();
            this.togglePause();
        }
    }

    handleKeyUp(e) {
        this.keys[e.key] = false;
    }

    handleTouchStart(e) {
        const touch = e.touches[0];
        this.touchStartX = touch.clientX;
    }

    handleTouchEnd(e) {
        if (!this.gameRunning || this.gamePaused) return;

        const touch = e.changedTouches[0];
        const deltaX = touch.clientX - this.touchStartX;
        const minSwipeDistance = 50;

        if (Math.abs(deltaX) > minSwipeDistance) {
            if (deltaX > 0) {
                this.moveRight();
            } else {
                this.moveLeft();
            }
        }
    }

    startGame() {
        this.gameRunning = true;
        this.gamePaused = false;
        this.score = 0;
        this.speed = 0;
        this.maxSpeed = 0;
        this.cars = [];
        this.carSpawnTimer = 0;
        this.roadOffset = 0;
        this.player.lane = 1;
        this.player.x = this.canvas.width / 2 - 15;

        this.hideAllOverlays();
        this.updateScore();
        this.gameLoop();
    }

    restartGame() {
        this.startGame();
    }

    togglePause() {
        if (!this.gameRunning) return;

        this.gamePaused = !this.gamePaused;
        if (this.gamePaused) {
            this.showPauseScreen();
        } else {
            this.hidePauseScreen();
            this.gameLoop();
        }
    }

    resumeGame() {
        this.gamePaused = false;
        this.hidePauseScreen();
        this.gameLoop();
    }

    moveLeft() {
        if (!this.gameRunning || this.gamePaused) return;
        if (this.player.lane > 0) {
            this.player.lane--;
            this.player.x = this.roadX + this.player.lane * this.laneWidth + this.laneWidth / 2 - this.player.width / 2;
        }
    }

    moveRight() {
        if (!this.gameRunning || this.gamePaused) return;
        if (this.player.lane < 2) {
            this.player.lane++;
            this.player.x = this.roadX + this.player.lane * this.laneWidth + this.laneWidth / 2 - this.player.width / 2;
        }
    }

    gameLoop() {
        if (!this.gameRunning || this.gamePaused) return;

        this.update();
        this.draw();

        requestAnimationFrame(() => this.gameLoop());
    }

    update() {
        // Handle keyboard input
        if (this.keys['ArrowLeft'] || this.keys['a'] || this.keys['A']) {
            this.moveLeft();
        }
        if (this.keys['ArrowRight'] || this.keys['d'] || this.keys['D']) {
            this.moveRight();
        }

        // Update speed and score
        this.speed = Math.min(5 + this.score / 100, 15);
        this.maxSpeed = Math.max(this.maxSpeed, this.speed);
        this.score += this.speed / 10;
        this.roadSpeed = 2 + this.speed / 3;

        // Update road animation
        this.roadOffset += this.roadSpeed;
        if (this.roadOffset >= 40) {
            this.roadOffset = 0;
        }

        // Spawn cars
        this.carSpawnTimer++;
        if (this.carSpawnTimer >= Math.max(30, this.carSpawnRate - this.speed * 3)) {
            this.spawnCar();
            this.carSpawnTimer = 0;
        }

        // Update cars
        this.cars.forEach(car => {
            car.y += this.roadSpeed + this.speed;
        });

        // Remove off-screen cars
        this.cars = this.cars.filter(car => car.y < this.canvas.height + 50);

        // Check collisions
        this.checkCollisions();

        // Update UI
        this.updateScore();
    }

    spawnCar() {
        const lanes = [0, 1, 2];
        const availableLanes = lanes.filter(lane => {
            // Check if there's already a car in this lane recently
            return !this.cars.some(car =>
                Math.abs(car.lane - lane) < 0.5 && car.y < 100
            );
        });

        if (availableLanes.length === 0) return;

        const lane = availableLanes[Math.floor(Math.random() * availableLanes.length)];
        const carTypes = [
            { color: '#ff6b6b', width: 25, height: 50 },
            { color: '#4ecdc4', width: 28, height: 55 },
            { color: '#45b7d1', width: 26, height: 52 },
            { color: '#f9ca24', width: 24, height: 48 },
            { color: '#6c5ce7', width: 27, height: 54 }
        ];

        const carType = carTypes[Math.floor(Math.random() * carTypes.length)];

        this.cars.push({
            x: this.roadX + lane * this.laneWidth + this.laneWidth / 2 - carType.width / 2,
            y: -carType.height,
            width: carType.width,
            height: carType.height,
            color: carType.color,
            lane: lane
        });
    }

    checkCollisions() {
        for (let car of this.cars) {
            if (this.isColliding(this.player, car)) {
                this.gameOver();
                return;
            }
        }
    }

    isColliding(rect1, rect2) {
        return rect1.x < rect2.x + rect2.width &&
               rect1.x + rect1.width > rect2.x &&
               rect1.y < rect2.y + rect2.height &&
               rect1.y + rect1.height > rect2.y;
    }

    draw() {
        // Clear canvas
        this.ctx.fillStyle = '#2d3748';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw background (grass)
        this.ctx.fillStyle = '#48bb78';
        this.ctx.fillRect(0, 0, this.roadX, this.canvas.height);
        this.ctx.fillRect(this.roadX + this.roadWidth, 0, this.roadX, this.canvas.height);

        // Draw road
        this.ctx.fillStyle = '#4a5568';
        this.ctx.fillRect(this.roadX, 0, this.roadWidth, this.canvas.height);

        // Draw road markings
        this.ctx.fillStyle = '#ffffff';
        for (let i = 0; i < 3; i++) {
            const x = this.roadX + i * this.laneWidth;
            if (i > 0) {
                // Lane dividers
                for (let y = -this.roadOffset; y < this.canvas.height; y += 40) {
                    this.ctx.fillRect(x - 2, y, 4, 20);
                }
            }
        }

        // Draw road edges
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fillRect(this.roadX - 4, 0, 4, this.canvas.height);
        this.ctx.fillRect(this.roadX + this.roadWidth, 0, 4, this.canvas.height);

        // Draw trees/scenery
        this.drawScenery();

        // Draw traffic cars
        this.cars.forEach(car => this.drawCar(car));

        // Draw player car
        this.drawPlayerCar();
    }

    drawScenery() {
        this.ctx.fillStyle = '#2d5016';
        // Left side trees
        for (let y = -this.roadOffset; y < this.canvas.height; y += 80) {
            this.ctx.fillRect(20, y, 15, 30);
            this.ctx.fillRect(this.canvas.width - 35, y, 15, 30);
        }

        // Tree tops
        this.ctx.fillStyle = '#38a169';
        for (let y = -this.roadOffset; y < this.canvas.height; y += 80) {
            this.ctx.beginPath();
            this.ctx.arc(27, y + 10, 12, 0, Math.PI * 2);
            this.ctx.fill();

            this.ctx.beginPath();
            this.ctx.arc(this.canvas.width - 27, y + 10, 12, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }

    drawCar(car) {
        // Car body
        this.ctx.fillStyle = car.color;
        this.ctx.fillRect(car.x, car.y, car.width, car.height);

        // Car details
        this.ctx.fillStyle = '#2d3748';
        this.ctx.fillRect(car.x + 2, car.y + 5, car.width - 4, car.height - 15);

        // Wheels
        this.ctx.fillStyle = '#1a202c';
        this.ctx.fillRect(car.x - 2, car.y + 8, 4, 8);
        this.ctx.fillRect(car.x + car.width - 2, car.y + 8, 4, 8);
        this.ctx.fillRect(car.x - 2, car.y + car.height - 16, 4, 8);
        this.ctx.fillRect(car.x + car.width - 2, car.y + car.height - 16, 4, 8);
    }

    drawPlayerCar() {
        // Player car body (red sports car)
        this.ctx.fillStyle = '#e53e3e';
        this.ctx.fillRect(this.player.x, this.player.y, this.player.width, this.player.height);

        // Car details
        this.ctx.fillStyle = '#2d3748';
        this.ctx.fillRect(this.player.x + 3, this.player.y + 8, this.player.width - 6, this.player.height - 20);

        // Headlights
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fillRect(this.player.x + 2, this.player.y, 6, 4);
        this.ctx.fillRect(this.player.x + this.player.width - 8, this.player.y, 6, 4);

        // Wheels
        this.ctx.fillStyle = '#1a202c';
        this.ctx.fillRect(this.player.x - 3, this.player.y + 10, 5, 10);
        this.ctx.fillRect(this.player.x + this.player.width - 2, this.player.y + 10, 5, 10);
        this.ctx.fillRect(this.player.x - 3, this.player.y + this.player.height - 20, 5, 10);
        this.ctx.fillRect(this.player.x + this.player.width - 2, this.player.y + this.player.height - 20, 5, 10);

        // Racing stripes
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fillRect(this.player.x + this.player.width / 2 - 1, this.player.y, 2, this.player.height);
    }

    drawInitialScene() {
        this.draw();
    }

    gameOver() {
        this.gameRunning = false;
        this.playCrashSound();

        // Add crash effect
        document.querySelector('.game-container').classList.add('crash');
        setTimeout(() => {
            document.querySelector('.game-container').classList.remove('crash');
        }, 500);

        // Check for new high score
        let isNewRecord = false;
        const finalScore = Math.floor(this.score);
        if (finalScore > this.highScore) {
            this.highScore = finalScore;
            localStorage.setItem('racingHighScore', this.highScore);
            this.updateHighScoreDisplay();
            isNewRecord = true;
        }

        this.showGameOverScreen(isNewRecord);
    }

    updateScore() {
        document.getElementById('score').textContent = Math.floor(this.score);
        document.getElementById('speed').textContent = Math.floor(this.speed * 10);
    }

    updateHighScoreDisplay() {
        document.getElementById('high-score').textContent = this.highScore;
    }

    // Screen management
    showStartScreen() {
        document.getElementById('start-screen').classList.remove('hidden');
    }

    showGameOverScreen(isNewRecord) {
        document.getElementById('final-score').textContent = Math.floor(this.score);
        document.getElementById('max-speed').textContent = Math.floor(this.maxSpeed * 10);
        if (isNewRecord) {
            document.getElementById('new-record').classList.remove('hidden');
        } else {
            document.getElementById('new-record').classList.add('hidden');
        }
        document.getElementById('game-over').classList.remove('hidden');
    }

    showPauseScreen() {
        document.getElementById('pause-screen').classList.remove('hidden');
    }

    hidePauseScreen() {
        document.getElementById('pause-screen').classList.add('hidden');
    }

    hideAllOverlays() {
        document.getElementById('start-screen').classList.add('hidden');
        document.getElementById('game-over').classList.add('hidden');
        document.getElementById('pause-screen').classList.add('hidden');
    }

    // Sound effects
    playEngineSound() {
        this.playTone(100 + this.speed * 10, 0.1, 'sawtooth');
    }

    playCrashSound() {
        // Multiple crash sounds for effect
        this.playTone(150, 0.3, 'sawtooth');
        setTimeout(() => this.playTone(100, 0.4, 'square'), 100);
        setTimeout(() => this.playTone(80, 0.5, 'triangle'), 200);
    }

    playTone(frequency, duration, type = 'sine') {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.value = frequency;
            oscillator.type = type;

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration);
        } catch (error) {
            // Fallback if audio context is not supported
            console.log('Audio not supported');
        }
    }
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    new RacingGame();
});
