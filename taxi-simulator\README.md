# 🚕 Taxi Simulator - City Driver

Một game mô phỏng taxi thực tế trong thành phố với AI passengers thông minh và hệ thống kinh tế phức tạp.

## 🚀 Cài đặt

### Yêu cầu hệ thống
- Python 3.7 trở lên
- Windows, macOS, hoặc Linux

### Cài đặt dependencies
```bash
pip install -r requirements.txt
```

Hoặc cài đặt thủ công:
```bash
pip install pygame
```

## 🎮 Chạy game

```bash
python main.py
```

## 🚕 Cách chơi

### 🎯 Mục tiêu
- Lái taxi trong thành phố để đón và trả khách
- Kiếm tiền từ cước phí taxi
- Quản lý nhiên liệu và danh tiếng
- Trở thành tài xế taxi giỏi nhất thành phố!

### 🎮 Điều khiển

#### 🚗 Lái xe
- **W/↑**: Tăng tốc
- **S/↓**: Phanh/Lùi
- **A/←**: Rẽ trái
- **D/→**: Rẽ phải

#### 🎛️ Chức năng taxi
- **Space**: Đón/Trả khách
- **R**: Đổ xăng (tốn tiền)
- **M**: Bật/Tắt đồng hồ cước
- **ESC**: Tạm dừng/Menu

## 📊 Tính năng

### 🏙️ Thành phố sống động
- **Bản đồ lớn**: Thành phố với đường phố, tòa nhà, landmarks
- **Hệ thống đường**: Đường ngang, dọc với vạch kẻ thực tế
- **Landmarks**: Sân bay, bệnh viện, trung tâm thương mại
- **Minimap**: Bản đồ thu nhỏ hiển thị vị trí

### 👥 AI Passengers thông minh
- **Personality**: Mỗi khách có tính cách khác nhau
  - **Patient**: Kiên nhẫn, tip cao
  - **Impatient**: Nóng tính, mất kiên nhẫn nhanh
  - **Generous**: Cho tip nhiều
  - **Demanding**: Khó tính, yêu cầu cao
- **Patience System**: Khách sẽ bỏ đi nếu chờ quá lâu
- **Satisfaction**: Hài lòng dựa trên tốc độ và chất lượng dịch vụ
- **Dynamic Spawning**: Khách xuất hiện nhiều hơn vào giờ cao điểm

### 💰 Hệ thống kinh tế
- **Fare System**: Cước phí dựa trên khoảng cách và thời gian
- **Tips**: Tip dựa trên satisfaction và personality
- **Fuel Cost**: Chi phí nhiên liệu thực tế
- **Reputation**: Danh tiếng ảnh hưởng đến số lượng khách

### 🚕 Physics thực tế
- **Realistic Driving**: Vật lý lái xe chân thực
- **Fuel Consumption**: Tiêu thụ nhiên liệu dựa trên tốc độ
- **Speed Control**: Tăng tốc, phanh, quay đầu tự nhiên
- **Collision**: Va chạm với tòa nhà và chướng ngại vật

### 📈 Progression System
- **Money Management**: Quản lý tiền bạc
- **Reputation**: Xây dựng danh tiếng tốt
- **Statistics**: Theo dõi số chuyến, tổng thu nhập
- **Day/Night Cycle**: Chu kỳ ngày đêm ảnh hưởng gameplay

## 🎯 Gameplay Tips

### 💡 Chiến lược kiếm tiền
1. **Đón khách nhanh**: Giảm thời gian chờ để tăng satisfaction
2. **Lái xe an toàn**: Tránh va chạm và lái mượt mà
3. **Quản lý nhiên liệu**: Đổ xăng khi cần thiết
4. **Chọn khách thông minh**: Ưu tiên khách đi xa hoặc đến landmarks
5. **Xây dựng reputation**: Dịch vụ tốt = nhiều khách hơn

### 🚗 Kỹ thuật lái xe
- **Smooth driving**: Lái mượt mà để khách hài lòng
- **Route planning**: Lên kế hoạch đường đi ngắn nhất
- **Speed management**: Không lái quá nhanh hoặc quá chậm
- **Fuel awareness**: Theo dõi nhiên liệu thường xuyên

### 📊 Đọc hiểu UI
- **Money**: Tiền hiện tại và tổng thu nhập
- **Fuel Gauge**: Mức nhiên liệu (màu xanh = đầy, đỏ = sắp hết)
- **Reputation**: Danh tiếng (ảnh hưởng spawn rate)
- **Meter**: Đồng hồ cước (ON khi có khách)
- **Minimap**: Vị trí taxi (vàng), khách (xanh), đích (đỏ)

## 🎨 Giao diện

### 🖥️ Main UI
- **Top Panel**: Tiền, nhiên liệu, tốc độ, thời gian
- **Minimap**: Góc dưới phải
- **Passenger Info**: Thông tin khách hiện tại
- **Meter Display**: Trạng thái đồng hồ cước

### 🗺️ Visual Elements
- **Taxi**: Xe vàng với đèn roof (xanh = rảnh, đỏ = có khách)
- **Passengers**: Chấm xanh với thanh patience
- **Destinations**: Chấm đỏ lớn
- **Landmarks**: Tòa nhà màu sắc khác nhau
- **Roads**: Đường xám với vạch trắng

## 🏆 Scoring System

### 💰 Fare Calculation
- **Base Fare**: Dựa trên khoảng cách
- **Distance Fare**: $0.50 per pixel traveled
- **Time Bonus**: Giao nhanh = bonus 10%
- **Satisfaction Multiplier**: Khách hài lòng = tip cao
- **Landmark Bonus**: Đến landmarks = fare cao hơn

### ⭐ Reputation System
- **Good Service**: +2 reputation (satisfaction > 80%)
- **Bad Service**: -3 reputation (satisfaction < 50%)
- **Abandoned Passenger**: -5 reputation
- **High Reputation**: Khách xuất hiện nhanh hơn
- **Low Reputation**: Ít khách hơn

## 🔧 Customization

### ⚙️ Game Settings
Chỉnh sửa constants trong `main.py`:
- `PASSENGER_SPAWN_INTERVAL`: Tần suất xuất hiện khách
- `FUEL_CONSUMPTION`: Mức tiêu thụ nhiên liệu
- `MAX_SPEED`: Tốc độ tối đa taxi
- `FARE_RATE`: Tỷ lệ cước phí

### 🎨 Visual Customization
- Thay đổi màu sắc taxi
- Tùy chỉnh kích thước thành phố
- Thêm landmarks mới
- Điều chỉnh UI layout

## 🐛 Troubleshooting

### ❌ Lỗi thường gặp
- **ImportError**: Cài đặt lại pygame
- **Lag**: Giảm FPS hoặc kích thước thành phố
- **Crash**: Kiểm tra Python version (3.7+)

### 🔍 Debug Tips
- Xem console để theo dõi passenger events
- Kiểm tra fuel và money thường xuyên
- Sử dụng minimap để navigation

## 📝 Game Mechanics

### 🚕 Taxi States
- **Available**: Đèn xanh, có thể đón khách
- **Occupied**: Đèn đỏ, đang chở khách
- **Out of Fuel**: Không thể di chuyển

### 👤 Passenger States
- **Waiting**: Chờ taxi, patience giảm dần
- **In Taxi**: Đang trên xe, satisfaction thay đổi
- **Delivered**: Đã được trả, tính tiền

### 🕐 Time System
- **Day Cycle**: 24 giờ = 240 giây thực
- **Rush Hours**: 7-9h và 17-19h có nhiều khách hơn
- **Night Time**: Ít khách hơn, fare cao hơn

## 🎮 Advanced Strategies

### 🏆 Pro Tips
1. **Rush Hour Strategy**: Tập trung kiếm tiền vào giờ cao điểm
2. **Landmark Routes**: Ưu tiên chuyến đi đến landmarks
3. **Fuel Management**: Đổ xăng khi fuel < 30%
4. **Reputation Building**: Ưu tiên service quality hơn speed
5. **Route Optimization**: Học thuộc bản đồ để đi đường tắt

### 📊 Statistics Tracking
- **Earnings per Hour**: Thu nhập theo giờ
- **Average Trip Time**: Thời gian trung bình mỗi chuyến
- **Customer Satisfaction**: Tỷ lệ khách hài lòng
- **Fuel Efficiency**: Hiệu quả sử dụng nhiên liệu

---

**Chúc bạn trở thành tài xế taxi xuất sắc! 🚕💨**
