#!/usr/bin/env python3
"""
Flight Simulator 3D
Advanced 3D Flight Simulation with realistic physics and graphics
"""

import pygame
import math
import numpy as np
from pygame import gfxdraw
import random
import time

# Initialize Pygame
pygame.init()

# Constants
SCREEN_WIDTH = 1200
SCREEN_HEIGHT = 800
FPS = 60

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
BLUE = (135, 206, 235)  # Sky blue
GREEN = (34, 139, 34)   # Forest green
BROWN = (139, 69, 19)   # Saddle brown
GRAY = (128, 128, 128)
RED = (255, 0, 0)
YELLOW = (255, 255, 0)
ORANGE = (255, 165, 0)

class Vector3D:
    """3D Vector class for 3D calculations"""
    def __init__(self, x=0, y=0, z=0):
        self.x = x
        self.y = y
        self.z = z

    def __add__(self, other):
        return Vector3D(self.x + other.x, self.y + other.y, self.z + other.z)

    def __sub__(self, other):
        return Vector3D(self.x - other.x, self.y - other.y, self.z - other.z)

    def __mul__(self, scalar):
        return Vector3D(self.x * scalar, self.y * scalar, self.z * scalar)

    def magnitude(self):
        return math.sqrt(self.x**2 + self.y**2 + self.z**2)

    def normalize(self):
        mag = self.magnitude()
        if mag > 0:
            return Vector3D(self.x/mag, self.y/mag, self.z/mag)
        return Vector3D(0, 0, 0)

class Camera3D:
    """3D Camera for rendering"""
    def __init__(self, position, target):
        self.position = Vector3D(position[0], position[1], position[2])
        self.target = Vector3D(target[0], target[1], target[2])
        self.up = Vector3D(0, 1, 0)
        self.fov = 60
        self.near = 0.1
        self.far = 1000

    def project_point(self, point_3d, screen_width, screen_height):
        """Project 3D point to 2D screen coordinates"""
        # Simple perspective projection
        if point_3d.z <= 0:
            return None

        # Distance from camera
        dx = point_3d.x - self.position.x
        dy = point_3d.y - self.position.y
        dz = point_3d.z - self.position.z

        # Perspective projection
        scale = 400 / (dz + 1)  # Simple perspective
        screen_x = screen_width // 2 + dx * scale
        screen_y = screen_height // 2 - dy * scale

        return (int(screen_x), int(screen_y))

class Aircraft3D:
    """3D Aircraft with realistic flight physics"""
    def __init__(self, x=0, y=100, z=0):
        # Position and orientation
        self.position = Vector3D(x, y, z)
        self.velocity = Vector3D(0, 0, 0)
        self.acceleration = Vector3D(0, 0, 0)

        # Rotation (pitch, yaw, roll in radians)
        self.pitch = 0  # Up/down rotation
        self.yaw = 0    # Left/right rotation
        self.roll = 0   # Banking rotation

        # Flight parameters
        self.throttle = 0.0  # 0.0 to 1.0
        self.speed = 0.0
        self.altitude = y
        self.fuel = 100.0

        # Control inputs
        self.elevator = 0.0  # Pitch control
        self.rudder = 0.0    # Yaw control
        self.aileron = 0.0   # Roll control

        # Aircraft properties
        self.mass = 1000  # kg
        self.wing_area = 20  # m²
        self.max_thrust = 5000  # N

        # Engine state
        self.engine_on = False
        self.stall_speed = 60  # km/h
        self.max_speed = 400   # km/h

        # Landing gear
        self.gear_down = True
        self.on_ground = True

class Terrain3D:
    """3D Terrain generation and rendering"""
    def __init__(self, size=100):
        self.size = size
        self.height_map = self.generate_height_map()
        self.airports = [
            {'pos': Vector3D(0, 0, 0), 'name': 'Main Airport', 'runway_length': 200},
            {'pos': Vector3D(500, 0, 300), 'name': 'Mountain Airport', 'runway_length': 150}
        ]

    def generate_height_map(self):
        """Generate random terrain height map"""
        height_map = []
        for x in range(self.size):
            row = []
            for z in range(self.size):
                # Simple noise-based terrain
                height = (math.sin(x * 0.1) * math.cos(z * 0.1) * 20 +
                         math.sin(x * 0.05) * 30 +
                         random.uniform(-5, 5))
                row.append(max(0, height))
            height_map.append(row)
        return height_map

    def get_height_at(self, x, z):
        """Get terrain height at specific coordinates"""
        grid_x = int(x // 10) % self.size
        grid_z = int(z // 10) % self.size
        return self.height_map[grid_x][grid_z]

class FlightSimulator3D:
    """Main Flight Simulator 3D class"""
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Flight Simulator 3D")
        self.clock = pygame.time.Clock()
        self.running = True

        # Game objects
        self.aircraft = Aircraft3D(0, 100, 0)
        self.camera = Camera3D((0, 120, -50), (0, 100, 0))
        self.terrain = Terrain3D()

        # Game state
        self.paused = False
        self.show_hud = True
        self.camera_mode = "cockpit"  # cockpit, external, chase

        # Weather
        self.wind = Vector3D(0, 0, 0)
        self.weather_type = "clear"  # clear, cloudy, stormy

        # Input handling
        self.keys = pygame.key.get_pressed()

        # Fonts
        self.font_large = pygame.font.Font(None, 36)
        self.font_medium = pygame.font.Font(None, 24)
        self.font_small = pygame.font.Font(None, 18)

        print("Flight Simulator 3D Initialized!")
        print("Controls:")
        print("  WASD - Pitch/Yaw")
        print("  Q/E - Roll")
        print("  Space - Throttle up")
        print("  Shift - Throttle down")
        print("  G - Toggle landing gear")
        print("  I - Start/Stop engine")
        print("  C - Change camera")
        print("  ESC - Quit")

    def handle_events(self):
        """Handle pygame events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False
                elif event.key == pygame.K_p:
                    self.paused = not self.paused
                elif event.key == pygame.K_h:
                    self.show_hud = not self.show_hud
                elif event.key == pygame.K_c:
                    self.cycle_camera()
                elif event.key == pygame.K_g:
                    self.aircraft.gear_down = not self.aircraft.gear_down
                elif event.key == pygame.K_i:
                    self.aircraft.engine_on = not self.aircraft.engine_on

    def cycle_camera(self):
        """Cycle through camera modes"""
        modes = ["cockpit", "external", "chase"]
        current_index = modes.index(self.camera_mode)
        self.camera_mode = modes[(current_index + 1) % len(modes)]

    def handle_input(self):
        """Handle continuous input"""
        if self.paused:
            return

        keys = pygame.key.get_pressed()

        # Flight controls
        if keys[pygame.K_w]:
            self.aircraft.elevator = min(1.0, self.aircraft.elevator + 0.02)
        elif keys[pygame.K_s]:
            self.aircraft.elevator = max(-1.0, self.aircraft.elevator - 0.02)
        else:
            self.aircraft.elevator *= 0.95  # Return to center

        if keys[pygame.K_a]:
            self.aircraft.rudder = min(1.0, self.aircraft.rudder + 0.02)
        elif keys[pygame.K_d]:
            self.aircraft.rudder = max(-1.0, self.aircraft.rudder - 0.02)
        else:
            self.aircraft.rudder *= 0.95

        if keys[pygame.K_q]:
            self.aircraft.aileron = min(1.0, self.aircraft.aileron + 0.02)
        elif keys[pygame.K_e]:
            self.aircraft.aileron = max(-1.0, self.aircraft.aileron - 0.02)
        else:
            self.aircraft.aileron *= 0.95

        # Throttle control
        if keys[pygame.K_SPACE]:
            if self.aircraft.engine_on:
                self.aircraft.throttle = min(1.0, self.aircraft.throttle + 0.01)
        elif keys[pygame.K_LSHIFT]:
            self.aircraft.throttle = max(0.0, self.aircraft.throttle - 0.02)

    def update_physics(self, dt):
        """Update aircraft physics"""
        if not self.aircraft.engine_on:
            self.aircraft.throttle = 0

        # Thrust calculation
        thrust = self.aircraft.throttle * self.aircraft.max_thrust

        # Drag calculation
        drag_coefficient = 0.02
        drag = drag_coefficient * self.aircraft.speed ** 2

        # Lift calculation
        if self.aircraft.speed > self.aircraft.stall_speed:
            lift = self.aircraft.speed * 0.1
        else:
            lift = 0

        # Gravity
        gravity = 9.81 * self.aircraft.mass

        # Update rotation based on control inputs
        self.aircraft.pitch += self.aircraft.elevator * dt * 2
        self.aircraft.yaw += self.aircraft.rudder * dt * 2
        self.aircraft.roll += self.aircraft.aileron * dt * 3

        # Limit rotations
        self.aircraft.pitch = max(-math.pi/3, min(math.pi/3, self.aircraft.pitch))
        self.aircraft.roll = max(-math.pi/2, min(math.pi/2, self.aircraft.roll))

        # Calculate forward direction based on yaw and pitch
        forward_x = math.sin(self.aircraft.yaw) * math.cos(self.aircraft.pitch)
        forward_y = math.sin(self.aircraft.pitch)
        forward_z = math.cos(self.aircraft.yaw) * math.cos(self.aircraft.pitch)

        # Update velocity
        net_force = thrust - drag
        acceleration = net_force / self.aircraft.mass

        self.aircraft.speed += acceleration * dt
        self.aircraft.speed = max(0, min(self.aircraft.max_speed, self.aircraft.speed))

        # Update position
        self.aircraft.position.x += forward_x * self.aircraft.speed * dt
        self.aircraft.position.y += (forward_y * self.aircraft.speed - gravity/100) * dt
        self.aircraft.position.z += forward_z * self.aircraft.speed * dt

        # Ground collision
        ground_height = self.terrain.get_height_at(self.aircraft.position.x, self.aircraft.position.z)
        if self.aircraft.position.y <= ground_height + 5:
            self.aircraft.position.y = ground_height + 5
            self.aircraft.on_ground = True
            if self.aircraft.speed > 100:  # Crash if landing too fast
                self.aircraft.speed = 0
                print("CRASH! Landing too fast!")
        else:
            self.aircraft.on_ground = False

        # Update altitude
        self.aircraft.altitude = self.aircraft.position.y

        # Fuel consumption
        if self.aircraft.engine_on:
            self.aircraft.fuel -= self.aircraft.throttle * dt * 2
            self.aircraft.fuel = max(0, self.aircraft.fuel)
            if self.aircraft.fuel <= 0:
                self.aircraft.engine_on = False

    def update_camera(self):
        """Update camera position based on mode"""
        if self.camera_mode == "cockpit":
            # Camera inside cockpit
            self.camera.position.x = self.aircraft.position.x
            self.camera.position.y = self.aircraft.position.y + 2
            self.camera.position.z = self.aircraft.position.z
        elif self.camera_mode == "chase":
            # Camera following behind aircraft
            offset_x = -math.sin(self.aircraft.yaw) * 50
            offset_z = -math.cos(self.aircraft.yaw) * 50
            self.camera.position.x = self.aircraft.position.x + offset_x
            self.camera.position.y = self.aircraft.position.y + 20
            self.camera.position.z = self.aircraft.position.z + offset_z
        elif self.camera_mode == "external":
            # Fixed external camera
            self.camera.position.x = self.aircraft.position.x + 100
            self.camera.position.y = self.aircraft.position.y + 50
            self.camera.position.z = self.aircraft.position.z + 100

    def render(self):
        """Render the 3D scene"""
        self.screen.fill(BLUE)  # Sky

        # Draw horizon
        horizon_y = SCREEN_HEIGHT // 2 + int(self.aircraft.pitch * 100)
        pygame.draw.rect(self.screen, GREEN, (0, horizon_y, SCREEN_WIDTH, SCREEN_HEIGHT - horizon_y))

        # Draw terrain
        self.draw_terrain()

        # Draw aircraft (if not in cockpit mode)
        if self.camera_mode != "cockpit":
            self.draw_aircraft()

        # Draw airports
        self.draw_airports()

        # Draw HUD
        if self.show_hud:
            self.draw_hud()

        # Draw cockpit (if in cockpit mode)
        if self.camera_mode == "cockpit":
            self.draw_cockpit()

        pygame.display.flip()

    def draw_terrain(self):
        """Draw 3D terrain"""
        # Simple terrain rendering
        for x in range(-10, 11):
            for z in range(-10, 11):
                world_x = self.aircraft.position.x + x * 50
                world_z = self.aircraft.position.z + z * 50
                height = self.terrain.get_height_at(world_x, world_z)

                point_3d = Vector3D(world_x, height, world_z)
                screen_pos = self.camera.project_point(point_3d, SCREEN_WIDTH, SCREEN_HEIGHT)

                if screen_pos:
                    color_intensity = max(50, min(255, 100 + height * 2))
                    color = (0, color_intensity, 0)
                    pygame.draw.circle(self.screen, color, screen_pos, 3)

    def draw_aircraft(self):
        """Draw 3D aircraft"""
        # Aircraft body points
        aircraft_points = [
            Vector3D(0, 0, 10),   # Nose
            Vector3D(-5, 0, -10), # Left wing
            Vector3D(5, 0, -10),  # Right wing
            Vector3D(0, 0, -15),  # Tail
            Vector3D(0, 5, -5),   # Top
        ]

        # Transform points based on aircraft rotation and position
        screen_points = []
        for point in aircraft_points:
            # Rotate point
            rotated_x = point.x * math.cos(self.aircraft.yaw) - point.z * math.sin(self.aircraft.yaw)
            rotated_z = point.x * math.sin(self.aircraft.yaw) + point.z * math.cos(self.aircraft.yaw)

            world_point = Vector3D(
                self.aircraft.position.x + rotated_x,
                self.aircraft.position.y + point.y,
                self.aircraft.position.z + rotated_z
            )

            screen_pos = self.camera.project_point(world_point, SCREEN_WIDTH, SCREEN_HEIGHT)
            if screen_pos:
                screen_points.append(screen_pos)

        # Draw aircraft
        if len(screen_points) >= 4:
            pygame.draw.polygon(self.screen, RED, screen_points[:4])

    def draw_airports(self):
        """Draw airports"""
        for airport in self.terrain.airports:
            screen_pos = self.camera.project_point(airport['pos'], SCREEN_WIDTH, SCREEN_HEIGHT)
            if screen_pos:
                pygame.draw.rect(self.screen, GRAY,
                               (screen_pos[0] - 20, screen_pos[1] - 5, 40, 10))

                # Airport name
                text = self.font_small.render(airport['name'], True, BLACK)
                self.screen.blit(text, (screen_pos[0] - 30, screen_pos[1] - 20))

    def draw_hud(self):
        """Draw HUD (Heads Up Display)"""
        # Background panel
        hud_rect = pygame.Rect(10, 10, 300, 200)
        pygame.draw.rect(self.screen, (0, 0, 0, 128), hud_rect)
        pygame.draw.rect(self.screen, WHITE, hud_rect, 2)

        # Flight data
        y_offset = 25
        hud_data = [
            f"Speed: {self.aircraft.speed:.1f} km/h",
            f"Altitude: {self.aircraft.altitude:.1f} m",
            f"Throttle: {self.aircraft.throttle*100:.0f}%",
            f"Fuel: {self.aircraft.fuel:.1f}%",
            f"Engine: {'ON' if self.aircraft.engine_on else 'OFF'}",
            f"Gear: {'DOWN' if self.aircraft.gear_down else 'UP'}",
            f"Ground: {'YES' if self.aircraft.on_ground else 'NO'}",
            f"Camera: {self.camera_mode.upper()}",
        ]

        for i, text in enumerate(hud_data):
            color = GREEN if i < 4 else WHITE
            if i == 4 and not self.aircraft.engine_on:
                color = RED
            if i == 3 and self.aircraft.fuel < 20:
                color = RED

            rendered_text = self.font_small.render(text, True, color)
            self.screen.blit(rendered_text, (20, y_offset + i * 20))

        # Attitude indicator (artificial horizon)
        self.draw_attitude_indicator()

        # Speed and altitude tapes
        self.draw_speed_tape()
        self.draw_altitude_tape()

        # Compass
        self.draw_compass()

    def draw_attitude_indicator(self):
        """Draw artificial horizon"""
        center_x = SCREEN_WIDTH - 150
        center_y = 150
        radius = 80

        # Background circle
        pygame.draw.circle(self.screen, BLACK, (center_x, center_y), radius)
        pygame.draw.circle(self.screen, WHITE, (center_x, center_y), radius, 2)

        # Horizon line
        horizon_offset = int(self.aircraft.pitch * 100)
        horizon_y = center_y + horizon_offset

        # Sky (blue) and ground (brown)
        if horizon_y < center_y + radius:
            pygame.draw.circle(self.screen, BLUE, (center_x, center_y), radius)
        if horizon_y > center_y - radius:
            ground_rect = pygame.Rect(center_x - radius, horizon_y, radius * 2, radius * 2)
            pygame.draw.rect(self.screen, BROWN, ground_rect)
            pygame.draw.circle(self.screen, BROWN, (center_x, center_y), radius)

        # Aircraft symbol
        pygame.draw.line(self.screen, YELLOW, (center_x - 20, center_y), (center_x + 20, center_y), 3)
        pygame.draw.line(self.screen, YELLOW, (center_x, center_y - 10), (center_x, center_y + 10), 3)

        # Roll indicator
        roll_x = center_x + math.sin(self.aircraft.roll) * 60
        roll_y = center_y - math.cos(self.aircraft.roll) * 60
        pygame.draw.line(self.screen, RED, (center_x, center_y - 70), (roll_x, roll_y), 2)

    def draw_speed_tape(self):
        """Draw speed indicator tape"""
        x = 50
        y = SCREEN_HEIGHT // 2
        height = 200

        # Background
        pygame.draw.rect(self.screen, BLACK, (x - 25, y - height//2, 50, height))
        pygame.draw.rect(self.screen, WHITE, (x - 25, y - height//2, 50, height), 2)

        # Speed markings
        for i in range(-5, 6):
            speed_val = int(self.aircraft.speed) + i * 20
            if speed_val >= 0:
                mark_y = y + i * 20
                if abs(mark_y - y) < height // 2:
                    pygame.draw.line(self.screen, WHITE, (x - 20, mark_y), (x - 10, mark_y), 1)
                    text = self.font_small.render(str(speed_val), True, WHITE)
                    self.screen.blit(text, (x - 45, mark_y - 8))

        # Current speed pointer
        pygame.draw.polygon(self.screen, YELLOW, [(x - 5, y), (x + 5, y - 5), (x + 5, y + 5)])

    def draw_altitude_tape(self):
        """Draw altitude indicator tape"""
        x = SCREEN_WIDTH - 50
        y = SCREEN_HEIGHT // 2
        height = 200

        # Background
        pygame.draw.rect(self.screen, BLACK, (x - 25, y - height//2, 50, height))
        pygame.draw.rect(self.screen, WHITE, (x - 25, y - height//2, 50, height), 2)

        # Altitude markings
        for i in range(-5, 6):
            alt_val = int(self.aircraft.altitude) + i * 50
            if alt_val >= 0:
                mark_y = y + i * 20
                if abs(mark_y - y) < height // 2:
                    pygame.draw.line(self.screen, WHITE, (x + 10, mark_y), (x + 20, mark_y), 1)
                    text = self.font_small.render(str(alt_val), True, WHITE)
                    self.screen.blit(text, (x + 25, mark_y - 8))

        # Current altitude pointer
        pygame.draw.polygon(self.screen, YELLOW, [(x + 5, y), (x - 5, y - 5), (x - 5, y + 5)])

    def draw_compass(self):
        """Draw compass/heading indicator"""
        center_x = SCREEN_WIDTH // 2
        center_y = 50
        radius = 30

        # Background
        pygame.draw.circle(self.screen, BLACK, (center_x, center_y), radius)
        pygame.draw.circle(self.screen, WHITE, (center_x, center_y), radius, 2)

        # Heading markings
        for i in range(0, 360, 30):
            angle_rad = math.radians(i - self.aircraft.yaw * 180 / math.pi)
            x = center_x + math.sin(angle_rad) * (radius - 10)
            y = center_y - math.cos(angle_rad) * (radius - 10)

            if i % 90 == 0:
                # Cardinal directions
                directions = ['N', 'E', 'S', 'W']
                text = directions[i // 90]
                rendered = self.font_small.render(text, True, WHITE)
                text_rect = rendered.get_rect(center=(x, y))
                self.screen.blit(rendered, text_rect)
            else:
                pygame.draw.circle(self.screen, WHITE, (int(x), int(y)), 2)

        # Aircraft heading pointer
        pygame.draw.polygon(self.screen, YELLOW,
                          [(center_x, center_y - radius + 5),
                           (center_x - 5, center_y - radius + 15),
                           (center_x + 5, center_y - radius + 15)])

    def draw_cockpit(self):
        """Draw cockpit view elements"""
        # Cockpit frame
        frame_color = (50, 50, 50)

        # Top frame
        pygame.draw.rect(self.screen, frame_color, (0, 0, SCREEN_WIDTH, 100))

        # Side frames
        pygame.draw.rect(self.screen, frame_color, (0, 0, 150, SCREEN_HEIGHT))
        pygame.draw.rect(self.screen, frame_color, (SCREEN_WIDTH - 150, 0, 150, SCREEN_HEIGHT))

        # Bottom frame (instrument panel)
        pygame.draw.rect(self.screen, frame_color, (0, SCREEN_HEIGHT - 150, SCREEN_WIDTH, 150))

        # Instrument panel details
        self.draw_instrument_panel()

    def draw_instrument_panel(self):
        """Draw detailed instrument panel"""
        panel_y = SCREEN_HEIGHT - 140

        # Engine instruments
        self.draw_engine_gauges(200, panel_y)

        # Flight instruments
        self.draw_flight_instruments(SCREEN_WIDTH // 2, panel_y)

        # Navigation instruments
        self.draw_nav_instruments(SCREEN_WIDTH - 200, panel_y)

    def draw_engine_gauges(self, x, y):
        """Draw engine monitoring gauges"""
        # RPM gauge
        self.draw_circular_gauge(x, y, 40, "RPM", self.aircraft.throttle * 100, 0, 100, RED)

        # Fuel gauge
        self.draw_circular_gauge(x, y + 80, 40, "FUEL", self.aircraft.fuel, 0, 100, GREEN)

    def draw_flight_instruments(self, x, y):
        """Draw primary flight instruments"""
        # Airspeed indicator
        self.draw_circular_gauge(x - 80, y, 50, "SPEED", self.aircraft.speed, 0, 400, BLUE)

        # Altitude indicator
        self.draw_circular_gauge(x + 80, y, 50, "ALT", self.aircraft.altitude, 0, 1000, GREEN)

    def draw_nav_instruments(self, x, y):
        """Draw navigation instruments"""
        # Heading indicator
        self.draw_circular_gauge(x, y, 40, "HDG", self.aircraft.yaw * 180 / math.pi, 0, 360, YELLOW)

    def draw_circular_gauge(self, x, y, radius, label, value, min_val, max_val, color):
        """Draw a circular gauge"""
        # Background
        pygame.draw.circle(self.screen, BLACK, (x, y), radius)
        pygame.draw.circle(self.screen, WHITE, (x, y), radius, 2)

        # Value needle
        normalized_value = (value - min_val) / (max_val - min_val)
        angle = normalized_value * 2 * math.pi - math.pi / 2

        needle_x = x + math.cos(angle) * (radius - 10)
        needle_y = y + math.sin(angle) * (radius - 10)

        pygame.draw.line(self.screen, color, (x, y), (needle_x, needle_y), 3)

        # Label
        text = self.font_small.render(label, True, WHITE)
        text_rect = text.get_rect(center=(x, y + radius + 15))
        self.screen.blit(text, text_rect)

        # Value text
        value_text = self.font_small.render(f"{value:.0f}", True, WHITE)
        value_rect = value_text.get_rect(center=(x, y))
        self.screen.blit(value_text, value_rect)

    def run(self):
        """Main game loop"""
        while self.running:
            dt = self.clock.tick(FPS) / 1000.0  # Delta time in seconds

            self.handle_events()
            self.handle_input()

            if not self.paused:
                self.update_physics(dt)
                self.update_camera()

            self.render()

        pygame.quit()

if __name__ == "__main__":
    simulator = FlightSimulator3D()
    simulator.run()
