// Flight Simulator - Complete Implementation
class FlightSimulator {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');

        // Aircraft properties
        this.aircraft = {
            x: 100,
            y: this.canvas.height - 100,
            width: 40,
            height: 20,
            angle: 0,
            speed: 0,
            altitude: 0,
            verticalSpeed: 0,
            fuel: 100,
            throttle: 0,
            isOnGround: true,
            hasLanded: false
        };

        // Physics
        this.gravity = 0.1;
        this.lift = 0;
        this.drag = 0.02;
        this.maxSpeed = 400;
        this.stallSpeed = 80;
        this.landingSpeed = 120;
        this.takeoffSpeed = 180;

        // Game state
        this.gameRunning = false;
        this.gamePaused = false;
        this.score = 0;
        this.mission = 1;
        this.missionComplete = false;

        // Cockpit controls
        this.engineStarted = false;
        this.autopilot = false;
        this.targetAltitude = 500;
        this.targetHeading = 90;
        this.takeoffMode = false;
        this.landingMode = false;

        // Controls
        this.keys = {};

        // World
        this.camera = { x: 0, y: 0 };
        this.airports = [
            { x: 50, y: this.canvas.height - 50, width: 200, height: 20, name: "Sân bay A" },
            { x: 600, y: this.canvas.height - 50, width: 200, height: 20, name: "Sân bay B" }
        ];
        this.obstacles = [
            { x: 300, y: this.canvas.height - 150, width: 50, height: 100, type: "building" },
            { x: 450, y: this.canvas.height - 200, width: 80, height: 150, type: "mountain" },
            { x: 550, y: this.canvas.height - 120, width: 40, height: 70, type: "building" }
        ];

        // Weather
        this.wind = { x: 0, y: 0 };
        this.weather = "clear";

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.showStartScreen();
        this.updateHUD();
        this.drawInitialScene();
    }

    setupEventListeners() {
        // Keyboard controls
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));

        // Button controls
        document.getElementById('start-btn').addEventListener('click', () => this.startGame());
        document.getElementById('restart-btn').addEventListener('click', () => this.restartGame());
        document.getElementById('next-mission-btn').addEventListener('click', () => this.nextMission());
        document.getElementById('restart-success-btn').addEventListener('click', () => this.restartGame());
        document.getElementById('resume-btn').addEventListener('click', () => this.resumeGame());
        document.getElementById('restart-pause-btn').addEventListener('click', () => this.restartGame());

        // Cockpit controls
        document.getElementById('engine-start').addEventListener('click', () => this.toggleEngine());
        document.getElementById('takeoff-btn').addEventListener('click', () => this.initiateTakeoff());
        document.getElementById('landing-btn').addEventListener('click', () => this.initiateLanding());
        document.getElementById('autopilot-btn').addEventListener('click', () => this.toggleAutopilot());
        document.getElementById('emergency-landing').addEventListener('click', () => this.emergencyLanding());
        document.getElementById('eject-btn').addEventListener('click', () => this.eject());

        // Throttle slider
        document.getElementById('throttle-slider').addEventListener('input', (e) => {
            if (this.engineStarted) {
                this.aircraft.throttle = parseInt(e.target.value);
                document.getElementById('throttle-value').textContent = e.target.value + '%';
            } else {
                e.target.value = 0;
                document.getElementById('throttle-value').textContent = '0%';
            }
        });

        // Navigation controls
        document.getElementById('target-altitude').addEventListener('input', (e) => {
            this.targetAltitude = parseInt(e.target.value);
        });

        document.getElementById('heading-slider').addEventListener('input', (e) => {
            this.targetHeading = parseInt(e.target.value);
            document.getElementById('heading-value').textContent = e.target.value + '°';
        });

        // Mobile controls
        document.querySelectorAll('.control-btn').forEach(btn => {
            btn.addEventListener('touchstart', (e) => this.handleMobileControl(e, true));
            btn.addEventListener('touchend', (e) => this.handleMobileControl(e, false));
            btn.addEventListener('mousedown', (e) => this.handleMobileControl(e, true));
            btn.addEventListener('mouseup', (e) => this.handleMobileControl(e, false));
        });
    }

    handleKeyDown(e) {
        this.keys[e.key] = true;

        if (e.key === 'Escape') {
            e.preventDefault();
            this.togglePause();
        }
    }

    handleKeyUp(e) {
        this.keys[e.key] = false;
    }

    handleMobileControl(e, isPressed) {
        e.preventDefault();
        const action = e.target.getAttribute('data-action');

        if (!isPressed && action === 'pause') {
            this.togglePause();
            return;
        }

        if (!this.gameRunning || this.gamePaused) return;

        switch (action) {
            case 'up':
                this.keys['ArrowUp'] = isPressed;
                break;
            case 'down':
                this.keys['ArrowDown'] = isPressed;
                break;
            case 'left':
                this.keys['ArrowLeft'] = isPressed;
                break;
            case 'right':
                this.keys['ArrowRight'] = isPressed;
                break;
            case 'throttle':
                this.keys[' '] = isPressed;
                break;
            case 'brake':
                this.keys['Shift'] = isPressed;
                break;
        }
    }

    startGame() {
        this.gameRunning = true;
        this.gamePaused = false;
        this.score = 0;
        this.mission = 1;
        this.missionComplete = false;

        // Reset aircraft
        this.aircraft = {
            x: 100,
            y: this.canvas.height - 100,
            width: 40,
            height: 20,
            angle: 0,
            speed: 0,
            altitude: 0,
            verticalSpeed: 0,
            fuel: 100,
            throttle: 0,
            isOnGround: true,
            hasLanded: false
        };

        this.camera = { x: 0, y: 0 };
        this.generateWeather();
        this.hideAllOverlays();
        this.updateMissionText();
        this.gameLoop();
    }

    restartGame() {
        this.startGame();
    }

    nextMission() {
        this.mission++;
        this.missionComplete = false;

        // Reset aircraft at destination airport
        const targetAirport = this.airports[this.mission % 2];
        this.aircraft.x = targetAirport.x + 50;
        this.aircraft.y = targetAirport.y - 50;
        this.aircraft.speed = 0;
        this.aircraft.altitude = 0;
        this.aircraft.isOnGround = true;
        this.aircraft.fuel = 100;

        this.generateWeather();
        this.hideAllOverlays();
        this.updateMissionText();
        this.gameLoop();
    }

    togglePause() {
        if (!this.gameRunning) return;

        this.gamePaused = !this.gamePaused;
        if (this.gamePaused) {
            this.showPauseScreen();
        } else {
            this.hidePauseScreen();
            this.gameLoop();
        }
    }

    resumeGame() {
        this.gamePaused = false;
        this.hidePauseScreen();
        this.gameLoop();
    }

    generateWeather() {
        const weatherTypes = ["clear", "windy", "stormy"];
        this.weather = weatherTypes[Math.floor(Math.random() * weatherTypes.length)];

        switch (this.weather) {
            case "clear":
                this.wind = { x: 0, y: 0 };
                break;
            case "windy":
                this.wind = { x: (Math.random() - 0.5) * 2, y: (Math.random() - 0.5) * 1 };
                break;
            case "stormy":
                this.wind = { x: (Math.random() - 0.5) * 4, y: (Math.random() - 0.5) * 2 };
                break;
        }
    }

    gameLoop() {
        if (!this.gameRunning || this.gamePaused) return;

        this.update();
        this.draw();
        this.updateHUD();

        requestAnimationFrame(() => this.gameLoop());
    }

    update() {
        this.handleInput();
        this.updatePhysics();
        this.updateCamera();
        this.checkCollisions();
        this.checkMissionStatus();
        this.updateScore();
    }

    // Cockpit Control Methods
    toggleEngine() {
        this.engineStarted = !this.engineStarted;
        const engineBtn = document.getElementById('engine-start');
        const indicator = document.getElementById('engine-indicator');

        if (this.engineStarted) {
            engineBtn.classList.add('active');
            indicator.classList.add('active');
            engineBtn.querySelector('.btn-label').textContent = 'Tắt máy';
            this.playEngineSound();
            this.updateFlightStatus('Động cơ hoạt động');
        } else {
            engineBtn.classList.remove('active');
            indicator.classList.remove('active');
            engineBtn.querySelector('.btn-label').textContent = 'Khởi động';
            this.aircraft.throttle = 0;
            document.getElementById('throttle-slider').value = 0;
            document.getElementById('throttle-value').textContent = '0%';
            this.updateFlightStatus('Động cơ tắt');
        }
    }

    initiateTakeoff() {
        if (!this.engineStarted) {
            this.updateFlightStatus('Khởi động động cơ trước!');
            return;
        }

        if (!this.aircraft.isOnGround) {
            this.updateFlightStatus('Đã bay rồi!');
            return;
        }

        this.takeoffMode = true;
        this.landingMode = false;
        this.autopilot = false;

        const takeoffBtn = document.getElementById('takeoff-btn');
        takeoffBtn.classList.add('active');
        takeoffBtn.querySelector('.btn-status').textContent = 'Đang cất cánh';

        // Auto throttle for takeoff
        this.aircraft.throttle = 80;
        document.getElementById('throttle-slider').value = 80;
        document.getElementById('throttle-value').textContent = '80%';

        this.updateFlightStatus('Cất cánh - Tăng ga!');

        // Auto takeoff sequence
        setTimeout(() => {
            if (this.takeoffMode && this.aircraft.speed > this.takeoffSpeed) {
                this.aircraft.angle = -0.2; // Pull up
                this.updateFlightStatus('Kéo mũi lên!');
            }
        }, 3000);
    }

    initiateLanding() {
        if (this.aircraft.isOnGround) {
            this.updateFlightStatus('Đã ở mặt đất!');
            return;
        }

        this.landingMode = true;
        this.takeoffMode = false;
        this.autopilot = true;

        const landingBtn = document.getElementById('landing-btn');
        landingBtn.classList.add('active');
        landingBtn.querySelector('.btn-status').textContent = 'Đang hạ cánh';

        this.updateFlightStatus('Chế độ hạ cánh tự động');

        // Find nearest airport
        const nearestAirport = this.findNearestAirport();
        this.targetAltitude = 0;
        document.getElementById('target-altitude').value = 0;
    }

    toggleAutopilot() {
        this.autopilot = !this.autopilot;
        const autopilotBtn = document.getElementById('autopilot-btn');
        const statusSpan = document.getElementById('autopilot-status');

        if (this.autopilot) {
            autopilotBtn.classList.add('active');
            statusSpan.textContent = 'Bật';
            this.updateFlightStatus('Tự động lái bật');
        } else {
            autopilotBtn.classList.remove('active');
            statusSpan.textContent = 'Tắt';
            this.updateFlightStatus('Tự động lái tắt');
        }
    }

    emergencyLanding() {
        this.landingMode = true;
        this.takeoffMode = false;
        this.autopilot = true;
        this.aircraft.throttle = 20;
        this.targetAltitude = 0;

        document.getElementById('throttle-slider').value = 20;
        document.getElementById('throttle-value').textContent = '20%';
        document.getElementById('target-altitude').value = 0;

        this.updateFlightStatus('HẠ CÁNH KHẨN CẤP!');
        this.playEmergencySound();
    }

    eject() {
        if (confirm('Bạn có chắc muốn nhảy dù? Game sẽ kết thúc!')) {
            this.gameOver('Phi công đã nhảy dù an toàn!');
        }
    }

    findNearestAirport() {
        let nearest = this.airports[0];
        let minDistance = Math.abs(this.aircraft.x - nearest.x);

        for (let airport of this.airports) {
            const distance = Math.abs(this.aircraft.x - airport.x);
            if (distance < minDistance) {
                minDistance = distance;
                nearest = airport;
            }
        }

        return nearest;
    }

    updateFlightStatus(status) {
        document.getElementById('flight-status').textContent = status;

        // Add status color coding
        const statusElement = document.getElementById('flight-status').parentElement;
        statusElement.classList.remove('warning', 'danger', 'success');

        if (status.includes('KHẨN CẤP') || status.includes('Tai nạn')) {
            statusElement.classList.add('danger');
        } else if (status.includes('Cảnh báo') || status.includes('Hết nhiên liệu')) {
            statusElement.classList.add('warning');
        } else if (status.includes('Thành công') || status.includes('Hạ cánh an toàn')) {
            statusElement.classList.add('success');
        }
    }

    updateHUD() {
        // Update instruments
        document.getElementById('altitude').textContent = Math.floor(this.aircraft.altitude);
        document.getElementById('speed').textContent = Math.floor(this.aircraft.speed);
        document.getElementById('fuel-percent').textContent = Math.floor(this.aircraft.fuel) + '%';

        // Update fuel bar
        const fuelFill = document.getElementById('fuel-fill');
        fuelFill.style.width = this.aircraft.fuel + '%';

        // Update attitude indicator
        const horizon = document.getElementById('horizon');
        const rotation = this.aircraft.angle * (180 / Math.PI);
        horizon.style.transform = `rotate(${rotation}deg)`;

        // Update throttle display
        document.getElementById('throttle-value').textContent = Math.floor(this.aircraft.throttle) + '%';

        // Update status based on flight conditions
        if (!this.gameRunning) return;

        if (this.aircraft.fuel <= 10) {
            this.updateFlightStatus('Cảnh báo: Sắp hết nhiên liệu!');
        } else if (this.aircraft.speed < this.stallSpeed && !this.aircraft.isOnGround) {
            this.updateFlightStatus('Cảnh báo: Tốc độ quá thấp!');
        } else if (this.takeoffMode && this.aircraft.speed > this.takeoffSpeed) {
            this.updateFlightStatus('Sẵn sàng cất cánh!');
        } else if (this.landingMode && this.aircraft.altitude < 100) {
            this.updateFlightStatus('Chuẩn bị hạ cánh...');
        }
    }

    drawInitialScene() {
        // Clear canvas with sky gradient
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#87CEEB');
        gradient.addColorStop(0.7, '#98FB98');
        gradient.addColorStop(1, '#90EE90');

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw ground
        this.ctx.fillStyle = '#8B4513';
        this.ctx.fillRect(0, this.canvas.height - 100, this.canvas.width, 100);

        // Draw simple aircraft
        this.ctx.fillStyle = '#ff0000';
        this.ctx.fillRect(this.aircraft.x, this.aircraft.y, this.aircraft.width, this.aircraft.height);
    }

    // Sound effects
    playEngineSound() {
        this.playTone(150, 0.5, 'sawtooth');
    }

    playEmergencySound() {
        // Emergency alarm
        for (let i = 0; i < 3; i++) {
            setTimeout(() => {
                this.playTone(800, 0.2, 'square');
                setTimeout(() => this.playTone(600, 0.2, 'square'), 100);
            }, i * 400);
        }
    }

    playTone(frequency, duration, type = 'sine') {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.value = frequency;
            oscillator.type = type;

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration);
        } catch (error) {
            console.log('Audio not supported');
        }
    }

    // Screen management methods
    showStartScreen() {
        document.getElementById('start-screen').classList.remove('hidden');
    }

    hideAllOverlays() {
        document.getElementById('start-screen').classList.add('hidden');
        document.getElementById('game-over').classList.add('hidden');
        document.getElementById('success-screen').classList.add('hidden');
        document.getElementById('pause-screen').classList.add('hidden');
    }

    gameOver(reason) {
        this.gameRunning = false;
        document.getElementById('game-over-message').textContent = reason;
        document.getElementById('final-score').textContent = Math.floor(this.score);
        document.getElementById('game-over').classList.remove('hidden');
    }

    missionSuccess() {
        this.missionComplete = true;
        document.getElementById('bonus-score').textContent = '1000';
        this.score += 1000;
        document.getElementById('total-score').textContent = Math.floor(this.score);
        document.getElementById('success-screen').classList.remove('hidden');
        this.updateFlightStatus('Nhiệm vụ hoàn thành!');
    }

    updateMissionText() {
        const targetAirport = this.airports[(this.mission - 1) % 2 === 0 ? 1 : 0];
        document.getElementById('mission-text').textContent =
            `Nhiệm vụ ${this.mission}: Bay đến ${targetAirport.name}`;
    }
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    new FlightSimulator();
});

    handleInput() {
        // Throttle control
        if (this.keys[' ']) {
            this.aircraft.throttle = Math.min(this.aircraft.throttle + 2, 100);
        } else if (this.keys['Shift']) {
            this.aircraft.throttle = Math.max(this.aircraft.throttle - 3, 0);
        } else {
            this.aircraft.throttle = Math.max(this.aircraft.throttle - 1, 0);
        }

        // Pitch control (up/down)
        if (this.keys['ArrowUp'] || this.keys['w'] || this.keys['W']) {
            this.aircraft.angle = Math.max(this.aircraft.angle - 0.02, -Math.PI/4);
        }
        if (this.keys['ArrowDown'] || this.keys['s'] || this.keys['S']) {
            this.aircraft.angle = Math.min(this.aircraft.angle + 0.02, Math.PI/4);
        }

        // Roll control (left/right) - affects horizontal movement
        if (this.keys['ArrowLeft'] || this.keys['a'] || this.keys['A']) {
            this.aircraft.x -= this.aircraft.speed * 0.01;
        }
        if (this.keys['ArrowRight'] || this.keys['d'] || this.keys['D']) {
            this.aircraft.x += this.aircraft.speed * 0.01;
        }
    }

    updatePhysics() {
        // Fuel consumption
        this.aircraft.fuel -= this.aircraft.throttle * 0.01;
        if (this.aircraft.fuel <= 0) {
            this.aircraft.fuel = 0;
            this.aircraft.throttle = 0;
        }

        // Speed calculation
        const thrust = this.aircraft.throttle * 0.5;
        this.aircraft.speed += thrust - this.drag * this.aircraft.speed;
        this.aircraft.speed = Math.max(0, Math.min(this.aircraft.speed, this.maxSpeed));

        // Lift calculation
        if (this.aircraft.speed > this.stallSpeed) {
            this.lift = this.aircraft.speed * 0.003;
        } else {
            this.lift = 0;
        }

        // Vertical speed calculation
        const pitchEffect = -Math.sin(this.aircraft.angle) * this.aircraft.speed * 0.01;
        this.aircraft.verticalSpeed += pitchEffect + this.lift - this.gravity;

        // Apply wind effects
        this.aircraft.x += this.wind.x;
        this.aircraft.verticalSpeed += this.wind.y * 0.1;

        // Update position
        this.aircraft.y -= this.aircraft.verticalSpeed;
        this.aircraft.x += Math.cos(this.aircraft.angle) * this.aircraft.speed * 0.02;

        // Calculate altitude
        const groundLevel = this.canvas.height - 100;
        this.aircraft.altitude = Math.max(0, groundLevel - this.aircraft.y);

        // Ground collision
        if (this.aircraft.y >= groundLevel) {
            this.aircraft.y = groundLevel;
            this.aircraft.isOnGround = true;
            this.aircraft.verticalSpeed = 0;

            // Landing check
            if (this.aircraft.speed < this.landingSpeed && this.aircraft.speed > 0) {
                this.aircraft.hasLanded = true;
            } else if (this.aircraft.speed >= this.landingSpeed) {
                // Crash landing
                this.gameOver("Hạ cánh quá nhanh! Tốc độ phải dưới " + this.landingSpeed + " km/h");
                return;
            }
        } else {
            this.aircraft.isOnGround = false;
            this.aircraft.hasLanded = false;
        }

        // Takeoff check
        if (this.aircraft.isOnGround && this.aircraft.speed > this.takeoffSpeed && this.aircraft.angle < -0.1) {
            this.aircraft.isOnGround = false;
        }

        // Stall check
        if (!this.aircraft.isOnGround && this.aircraft.speed < this.stallSpeed) {
            this.aircraft.verticalSpeed -= 0.5; // Rapid descent
        }

        // Bounds checking
        if (this.aircraft.x < 0) this.aircraft.x = 0;
        if (this.aircraft.x > 1000) this.aircraft.x = 1000;
        if (this.aircraft.y < 0) this.aircraft.y = 0;
    }

    updateCamera() {
        // Follow aircraft
        this.camera.x = this.aircraft.x - this.canvas.width / 2;
        this.camera.y = this.aircraft.y - this.canvas.height / 2;

        // Clamp camera
        this.camera.x = Math.max(0, Math.min(this.camera.x, 1000 - this.canvas.width));
        this.camera.y = Math.max(0, Math.min(this.camera.y, this.canvas.height - this.canvas.height));
    }

    checkCollisions() {
        for (let obstacle of this.obstacles) {
            if (this.aircraft.x < obstacle.x + obstacle.width &&
                this.aircraft.x + this.aircraft.width > obstacle.x &&
                this.aircraft.y < obstacle.y + obstacle.height &&
                this.aircraft.y + this.aircraft.height > obstacle.y) {
                this.gameOver("Va chạm với " + (obstacle.type === "building" ? "tòa nhà" : "núi") + "!");
                return;
            }
        }

        // Fuel check
        if (this.aircraft.fuel <= 0 && !this.aircraft.isOnGround) {
            this.gameOver("Hết nhiên liệu!");
            return;
        }
    }

    checkMissionStatus() {
        const targetAirport = this.airports[(this.mission - 1) % 2 === 0 ? 1 : 0];

        if (this.aircraft.hasLanded &&
            this.aircraft.x > targetAirport.x &&
            this.aircraft.x < targetAirport.x + targetAirport.width &&
            this.aircraft.y >= targetAirport.y - 30) {
            this.missionSuccess();
        }
    }

    updateScore() {
        if (this.gameRunning && !this.aircraft.isOnGround) {
            this.score += 1;
        }
    }

    draw() {
        // Clear canvas with sky gradient
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#87CEEB');
        gradient.addColorStop(0.7, '#98FB98');
        gradient.addColorStop(1, '#90EE90');

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw clouds
        this.drawClouds();

        // Save context for camera transform
        this.ctx.save();
        this.ctx.translate(-this.camera.x, -this.camera.y);

        // Draw airports
        this.drawAirports();

        // Draw obstacles
        this.drawObstacles();

        // Draw aircraft
        this.drawAircraft();

        // Restore context
        this.ctx.restore();

        // Draw weather effects
        this.drawWeather();
    }
