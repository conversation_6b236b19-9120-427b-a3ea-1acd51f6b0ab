#!/usr/bin/env python3
"""
Taxi Simulator - City Taxi Driver Game
Advanced taxi simulation with realistic city driving and passenger management
"""

import pygame
import math
import random
import time
from enum import Enum
from dataclasses import dataclass
from typing import List, Tuple, Optional

# Initialize Pygame
pygame.init()

# Constants
SCREEN_WIDTH = 1200
SCREEN_HEIGHT = 800
FPS = 60

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
YELLOW = (255, 255, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
GRAY = (128, 128, 128)
DARK_GRAY = (64, 64, 64)
LIGHT_GRAY = (192, 192, 192)
ORANGE = (255, 165, 0)
PURPLE = (128, 0, 128)
BROWN = (139, 69, 19)

class GameState(Enum):
    MENU = "menu"
    PLAYING = "playing"
    PAUSED = "paused"
    GAME_OVER = "game_over"

class PassengerState(Enum):
    WAITING = "waiting"
    IN_TAXI = "in_taxi"
    DELIVERED = "delivered"

@dataclass
class Vector2D:
    x: float
    y: float

    def __add__(self, other):
        return Vector2D(self.x + other.x, self.y + other.y)

    def __sub__(self, other):
        return Vector2D(self.x - other.x, self.y - other.y)

    def __mul__(self, scalar):
        return Vector2D(self.x * scalar, self.y * scalar)

    def magnitude(self):
        return math.sqrt(self.x**2 + self.y**2)

    def normalize(self):
        mag = self.magnitude()
        if mag > 0:
            return Vector2D(self.x/mag, self.y/mag)
        return Vector2D(0, 0)

    def distance_to(self, other):
        return (self - other).magnitude()

class Passenger:
    """Passenger class with AI behavior"""
    def __init__(self, pickup_pos: Vector2D, destination: Vector2D, name: str, fare: float):
        self.pickup_pos = pickup_pos
        self.destination = destination
        self.name = name
        self.base_fare = fare
        self.state = PassengerState.WAITING
        self.patience = 100.0  # Patience level (0-100)
        self.satisfaction = 100.0  # Satisfaction level
        self.wait_time = 0.0
        self.trip_time = 0.0
        self.tip_multiplier = 1.0

        # Passenger personality
        self.personality = random.choice(['patient', 'impatient', 'generous', 'demanding'])
        self.adjust_personality()

    def adjust_personality(self):
        """Adjust passenger stats based on personality"""
        if self.personality == 'patient':
            self.patience = 150.0
            self.tip_multiplier = 1.2
        elif self.personality == 'impatient':
            self.patience = 50.0
            self.tip_multiplier = 0.8
        elif self.personality == 'generous':
            self.tip_multiplier = 1.5
        elif self.personality == 'demanding':
            self.patience = 75.0
            self.tip_multiplier = 0.9

    def update(self, dt: float):
        """Update passenger state"""
        if self.state == PassengerState.WAITING:
            self.wait_time += dt
            # Decrease patience while waiting
            patience_loss = 5.0 * dt
            if self.personality == 'impatient':
                patience_loss *= 2
            self.patience -= patience_loss

        elif self.state == PassengerState.IN_TAXI:
            self.trip_time += dt
            # Satisfaction decreases with long trips
            if self.trip_time > 30:  # 30 seconds
                self.satisfaction -= 2.0 * dt

    def get_final_fare(self):
        """Calculate final fare including tips and penalties"""
        base = self.base_fare

        # Time bonus/penalty
        if self.trip_time < 20:  # Fast delivery
            base *= 1.1
        elif self.trip_time > 60:  # Slow delivery
            base *= 0.9

        # Satisfaction bonus
        if self.satisfaction > 80:
            base *= self.tip_multiplier
        elif self.satisfaction < 50:
            base *= 0.8

        return max(base * 0.5, base)  # Minimum 50% of base fare

class Taxi:
    """Player's taxi with realistic physics"""
    def __init__(self, x: float, y: float):
        self.position = Vector2D(x, y)
        self.velocity = Vector2D(0, 0)
        self.angle = 0.0  # Rotation angle in radians
        self.speed = 0.0
        self.max_speed = 200.0  # pixels per second
        self.acceleration = 150.0
        self.deceleration = 200.0
        self.turn_speed = 3.0  # radians per second

        # Taxi properties
        self.fuel = 100.0
        self.fuel_consumption = 5.0  # fuel per second when moving
        self.damage = 0.0  # 0-100, affects performance
        self.passenger = None

        # Visual properties
        self.width = 30
        self.height = 15
        self.color = YELLOW

        # Taxi meter
        self.meter_running = False
        self.trip_distance = 0.0
        self.fare_rate = 0.5  # money per pixel traveled

class City:
    """City map with roads, buildings, and landmarks"""
    def __init__(self, width: int, height: int):
        self.width = width
        self.height = height
        self.roads = []
        self.buildings = []
        self.landmarks = []
        self.traffic_lights = []

        self.generate_city()

    def generate_city(self):
        """Generate city layout"""
        # Create grid of roads
        road_spacing = 150
        road_width = 20

        # Horizontal roads
        for y in range(road_spacing, self.height, road_spacing):
            self.roads.append({
                'type': 'horizontal',
                'x': 0,
                'y': y,
                'width': self.width,
                'height': road_width
            })

        # Vertical roads
        for x in range(road_spacing, self.width, road_spacing):
            self.roads.append({
                'type': 'vertical',
                'x': x,
                'y': 0,
                'width': road_width,
                'height': self.height
            })

        # Generate buildings between roads
        self.generate_buildings()

        # Generate landmarks (pickup/dropoff points)
        self.generate_landmarks()

    def generate_buildings(self):
        """Generate buildings in city blocks"""
        block_size = 150
        building_margin = 30

        for x in range(0, self.width, block_size):
            for y in range(0, self.height, block_size):
                # Skip if this is a road intersection
                if x % block_size == 0 or y % block_size == 0:
                    continue

                # Create random buildings in this block
                for _ in range(random.randint(1, 3)):
                    bx = x + random.randint(building_margin, block_size - building_margin - 40)
                    by = y + random.randint(building_margin, block_size - building_margin - 40)
                    bw = random.randint(20, 60)
                    bh = random.randint(20, 60)

                    # Make sure building doesn't overlap roads
                    if (bx + bw < x + block_size - building_margin and
                        by + bh < y + block_size - building_margin):

                        building_type = random.choice(['residential', 'commercial', 'office'])
                        self.buildings.append({
                            'x': bx, 'y': by, 'width': bw, 'height': bh,
                            'type': building_type,
                            'color': self.get_building_color(building_type)
                        })

    def get_building_color(self, building_type: str):
        """Get color based on building type"""
        colors = {
            'residential': LIGHT_GRAY,
            'commercial': BLUE,
            'office': DARK_GRAY
        }
        return colors.get(building_type, GRAY)

    def generate_landmarks(self):
        """Generate important locations for pickups/dropoffs"""
        landmark_types = [
            ('Airport', ORANGE, 2.0),
            ('Hospital', RED, 1.5),
            ('Mall', PURPLE, 1.3),
            ('Hotel', BROWN, 1.4),
            ('Station', GREEN, 1.2)
        ]

        for i, (name, color, fare_multiplier) in enumerate(landmark_types):
            # Place landmarks at strategic locations
            x = random.randint(100, self.width - 100)
            y = random.randint(100, self.height - 100)

            self.landmarks.append({
                'name': name,
                'position': Vector2D(x, y),
                'color': color,
                'fare_multiplier': fare_multiplier,
                'size': 40
            })

class TaxiSimulator:
    """Main game class"""
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("🚕 Taxi Simulator - City Driver")
        self.clock = pygame.time.Clock()
        self.running = True

        # Game state
        self.state = GameState.MENU
        self.paused = False

        # Game world
        self.city = City(2000, 1500)  # Larger than screen for scrolling
        self.taxi = Taxi(100, 100)
        self.camera = Vector2D(0, 0)

        # Passengers and economy
        self.passengers = []
        self.active_passenger = None
        self.money = 100.0
        self.total_earnings = 0.0
        self.trips_completed = 0
        self.reputation = 100.0  # Affects passenger spawn rate

        # Game mechanics
        self.passenger_spawn_timer = 0.0
        self.passenger_spawn_interval = 10.0  # seconds
        self.game_time = 0.0
        self.day_time = 0.0  # 0-24 hours

        # UI
        self.font_large = pygame.font.Font(None, 36)
        self.font_medium = pygame.font.Font(None, 24)
        self.font_small = pygame.font.Font(None, 18)

        # Input
        self.keys = pygame.key.get_pressed()

        print("🚕 Taxi Simulator Initialized!")
        print("Controls:")
        print("  WASD - Drive taxi")
        print("  Space - Pick up/Drop off passenger")
        print("  R - Refuel (at gas stations)")
        print("  M - Toggle meter")
        print("  ESC - Pause/Menu")

    def handle_events(self):
        """Handle pygame events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    if self.state == GameState.PLAYING:
                        self.state = GameState.PAUSED
                    elif self.state == GameState.PAUSED:
                        self.state = GameState.PLAYING
                    elif self.state == GameState.MENU:
                        self.running = False
                elif event.key == pygame.K_RETURN:
                    if self.state == GameState.MENU:
                        self.start_game()
                    elif self.state == GameState.GAME_OVER:
                        self.start_game()
                elif event.key == pygame.K_SPACE:
                    if self.state == GameState.PLAYING:
                        self.handle_passenger_interaction()
                elif event.key == pygame.K_m:
                    if self.state == GameState.PLAYING:
                        self.taxi.meter_running = not self.taxi.meter_running
                elif event.key == pygame.K_r:
                    if self.state == GameState.PLAYING:
                        self.refuel_taxi()

    def start_game(self):
        """Start new game"""
        self.state = GameState.PLAYING
        self.money = 100.0
        self.total_earnings = 0.0
        self.trips_completed = 0
        self.reputation = 100.0
        self.taxi.fuel = 100.0
        self.taxi.damage = 0.0
        self.passengers.clear()
        self.active_passenger = None
        self.game_time = 0.0
        print("🚀 New game started!")

    def update(self, dt: float):
        """Update game state"""
        self.game_time += dt
        self.day_time = (self.game_time / 10) % 24  # 10 seconds = 1 hour

        # Update taxi
        self.update_taxi(dt)

        # Update passengers
        self.update_passengers(dt)

        # Spawn new passengers
        self.spawn_passengers(dt)

        # Update camera to follow taxi
        self.update_camera()

        # Check game over conditions
        if self.taxi.fuel <= 0 and self.money < 10:
            self.state = GameState.GAME_OVER

    def update_taxi(self, dt: float):
        """Update taxi physics and state"""
        keys = pygame.key.get_pressed()

        # Steering
        if keys[pygame.K_a] or keys[pygame.K_LEFT]:
            self.taxi.angle -= self.taxi.turn_speed * dt
        if keys[pygame.K_d] or keys[pygame.K_RIGHT]:
            self.taxi.angle += self.taxi.turn_speed * dt

        # Acceleration/Deceleration
        if keys[pygame.K_w] or keys[pygame.K_UP]:
            self.taxi.speed = min(self.taxi.max_speed,
                                self.taxi.speed + self.taxi.acceleration * dt)
        elif keys[pygame.K_s] or keys[pygame.K_DOWN]:
            self.taxi.speed = max(-self.taxi.max_speed * 0.5,
                                self.taxi.speed - self.taxi.deceleration * dt)
        else:
            # Natural deceleration
            if self.taxi.speed > 0:
                self.taxi.speed = max(0, self.taxi.speed - self.taxi.deceleration * 0.5 * dt)
            else:
                self.taxi.speed = min(0, self.taxi.speed + self.taxi.deceleration * 0.5 * dt)

        # Update position
        if abs(self.taxi.speed) > 0.1:
            # Calculate velocity based on angle and speed
            self.taxi.velocity.x = math.cos(self.taxi.angle) * self.taxi.speed
            self.taxi.velocity.y = math.sin(self.taxi.angle) * self.taxi.speed

            # Update position
            old_pos = Vector2D(self.taxi.position.x, self.taxi.position.y)
            self.taxi.position = self.taxi.position + self.taxi.velocity * dt

            # Keep taxi within city bounds
            self.taxi.position.x = max(0, min(self.city.width, self.taxi.position.x))
            self.taxi.position.y = max(0, min(self.city.height, self.taxi.position.y))

            # Update trip distance if meter is running
            if self.taxi.meter_running and self.active_passenger:
                distance_traveled = old_pos.distance_to(self.taxi.position)
                self.taxi.trip_distance += distance_traveled

            # Consume fuel
            fuel_consumption = self.taxi.fuel_consumption * dt * (abs(self.taxi.speed) / self.taxi.max_speed)
            self.taxi.fuel = max(0, self.taxi.fuel - fuel_consumption)

    def update_passengers(self, dt: float):
        """Update all passengers"""
        for passenger in self.passengers[:]:  # Copy list to avoid modification during iteration
            passenger.update(dt)

            # Remove passengers who lost all patience
            if passenger.patience <= 0 and passenger.state == PassengerState.WAITING:
                self.passengers.remove(passenger)
                self.reputation -= 5  # Reputation penalty
                print(f"❌ {passenger.name} left due to long wait!")

    def spawn_passengers(self, dt: float):
        """Spawn new passengers based on time and reputation"""
        self.passenger_spawn_timer += dt

        # Adjust spawn rate based on reputation and time of day
        spawn_rate = self.passenger_spawn_interval
        if self.reputation > 80:
            spawn_rate *= 0.8  # Faster spawning with good reputation
        elif self.reputation < 50:
            spawn_rate *= 1.5  # Slower spawning with bad reputation

        # More passengers during rush hours (7-9 AM, 5-7 PM)
        if (7 <= self.day_time <= 9) or (17 <= self.day_time <= 19):
            spawn_rate *= 0.6

        if self.passenger_spawn_timer >= spawn_rate and len(self.passengers) < 5:
            self.spawn_passenger()
            self.passenger_spawn_timer = 0

    def spawn_passenger(self):
        """Spawn a new passenger"""
        # Random pickup location near roads
        pickup_x = random.randint(50, self.city.width - 50)
        pickup_y = random.randint(50, self.city.height - 50)
        pickup_pos = Vector2D(pickup_x, pickup_y)

        # Random destination (prefer landmarks)
        if random.random() < 0.4 and self.city.landmarks:  # 40% chance to go to landmark
            landmark = random.choice(self.city.landmarks)
            destination = Vector2D(landmark['position'].x, landmark['position'].y)
            fare_multiplier = landmark['fare_multiplier']
        else:
            dest_x = random.randint(50, self.city.width - 50)
            dest_y = random.randint(50, self.city.height - 50)
            destination = Vector2D(dest_x, dest_y)
            fare_multiplier = 1.0

        # Calculate base fare based on distance
        distance = pickup_pos.distance_to(destination)
        base_fare = (distance * 0.1 + 5) * fare_multiplier  # Base fare formula

        # Generate passenger name
        names = ["Alice", "Bob", "Charlie", "Diana", "Eve", "Frank", "Grace", "Henry"]
        name = random.choice(names)

        passenger = Passenger(pickup_pos, destination, name, base_fare)
        self.passengers.append(passenger)

        print(f"📞 New passenger: {name} wants to go from ({pickup_x:.0f}, {pickup_y:.0f}) to ({destination.x:.0f}, {destination.y:.0f})")

    def handle_passenger_interaction(self):
        """Handle picking up or dropping off passengers"""
        if self.active_passenger is None:
            # Try to pick up a passenger
            for passenger in self.passengers:
                if passenger.state == PassengerState.WAITING:
                    distance = self.taxi.position.distance_to(passenger.pickup_pos)
                    if distance < 30:  # Close enough to pick up
                        self.pickup_passenger(passenger)
                        break
        else:
            # Try to drop off current passenger
            distance = self.taxi.position.distance_to(self.active_passenger.destination)
            if distance < 30:  # Close enough to drop off
                self.dropoff_passenger()

    def pickup_passenger(self, passenger: Passenger):
        """Pick up a passenger"""
        passenger.state = PassengerState.IN_TAXI
        self.active_passenger = passenger
        self.taxi.meter_running = True
        self.taxi.trip_distance = 0
        print(f"✅ Picked up {passenger.name}! Destination: ({passenger.destination.x:.0f}, {passenger.destination.y:.0f})")

    def dropoff_passenger(self):
        """Drop off current passenger"""
        if self.active_passenger:
            self.active_passenger.state = PassengerState.DELIVERED
            fare = self.active_passenger.get_final_fare()

            # Add distance-based fare
            distance_fare = self.taxi.trip_distance * self.taxi.fare_rate
            total_fare = fare + distance_fare

            self.money += total_fare
            self.total_earnings += total_fare
            self.trips_completed += 1

            # Reputation adjustment
            if self.active_passenger.satisfaction > 80:
                self.reputation += 2
            elif self.active_passenger.satisfaction < 50:
                self.reputation -= 3

            print(f"💰 Dropped off {self.active_passenger.name}! Earned: ${total_fare:.2f}")
            print(f"   Satisfaction: {self.active_passenger.satisfaction:.0f}%")

            # Remove passenger from list
            if self.active_passenger in self.passengers:
                self.passengers.remove(self.active_passenger)

            self.active_passenger = None
            self.taxi.meter_running = False
            self.taxi.trip_distance = 0

    def refuel_taxi(self):
        """Refuel taxi (costs money)"""
        if self.money >= 20:
            fuel_needed = 100 - self.taxi.fuel
            cost = fuel_needed * 0.2  # $0.2 per fuel unit
            if self.money >= cost:
                self.money -= cost
                self.taxi.fuel = 100
                print(f"⛽ Refueled! Cost: ${cost:.2f}")
            else:
                print("❌ Not enough money to refuel!")
        else:
            print("❌ Not enough money to refuel!")

    def update_camera(self):
        """Update camera to follow taxi"""
        # Center camera on taxi
        self.camera.x = self.taxi.position.x - SCREEN_WIDTH // 2
        self.camera.y = self.taxi.position.y - SCREEN_HEIGHT // 2

        # Keep camera within city bounds
        self.camera.x = max(0, min(self.city.width - SCREEN_WIDTH, self.camera.x))
        self.camera.y = max(0, min(self.city.height - SCREEN_HEIGHT, self.camera.y))

    def render(self):
        """Render the game"""
        if self.state == GameState.MENU:
            self.render_menu()
        elif self.state == GameState.PLAYING:
            self.render_game()
        elif self.state == GameState.PAUSED:
            self.render_game()
            self.render_pause_overlay()
        elif self.state == GameState.GAME_OVER:
            self.render_game_over()

        pygame.display.flip()

    def render_menu(self):
        """Render main menu"""
        self.screen.fill(BLACK)

        # Title
        title = self.font_large.render("🚕 TAXI SIMULATOR", True, YELLOW)
        title_rect = title.get_rect(center=(SCREEN_WIDTH//2, 200))
        self.screen.blit(title, title_rect)

        # Subtitle
        subtitle = self.font_medium.render("City Taxi Driver", True, WHITE)
        subtitle_rect = subtitle.get_rect(center=(SCREEN_WIDTH//2, 250))
        self.screen.blit(subtitle, subtitle_rect)

        # Instructions
        instructions = [
            "Drive your taxi around the city",
            "Pick up passengers and earn money",
            "Manage fuel and reputation",
            "",
            "Controls:",
            "WASD - Drive",
            "Space - Pick up/Drop off",
            "R - Refuel",
            "M - Toggle meter",
            "",
            "Press ENTER to start",
            "Press ESC to quit"
        ]

        y = 320
        for instruction in instructions:
            if instruction:
                color = YELLOW if instruction.startswith("Controls:") else WHITE
                text = self.font_small.render(instruction, True, color)
                text_rect = text.get_rect(center=(SCREEN_WIDTH//2, y))
                self.screen.blit(text, text_rect)
            y += 25

    def render_game(self):
        """Render main game"""
        # Clear screen with road color
        self.screen.fill(DARK_GRAY)

        # Render city
        self.render_city()

        # Render passengers
        self.render_passengers()

        # Render taxi
        self.render_taxi()

        # Render UI
        self.render_ui()

    def render_city(self):
        """Render city map"""
        # Render roads
        for road in self.city.roads:
            screen_x = road['x'] - self.camera.x
            screen_y = road['y'] - self.camera.y

            # Only render if visible on screen
            if (-50 < screen_x < SCREEN_WIDTH + 50 and -50 < screen_y < SCREEN_HEIGHT + 50):
                pygame.draw.rect(self.screen, GRAY,
                               (screen_x, screen_y, road['width'], road['height']))

                # Road markings
                if road['type'] == 'horizontal':
                    # Dashed center line
                    for x in range(0, road['width'], 40):
                        pygame.draw.rect(self.screen, WHITE,
                                       (screen_x + x, screen_y + road['height']//2 - 1, 20, 2))
                else:  # vertical
                    for y in range(0, road['height'], 40):
                        pygame.draw.rect(self.screen, WHITE,
                                       (screen_x + road['width']//2 - 1, screen_y + y, 2, 20))

        # Render buildings
        for building in self.city.buildings:
            screen_x = building['x'] - self.camera.x
            screen_y = building['y'] - self.camera.y

            if (-50 < screen_x < SCREEN_WIDTH + 50 and -50 < screen_y < SCREEN_HEIGHT + 50):
                pygame.draw.rect(self.screen, building['color'],
                               (screen_x, screen_y, building['width'], building['height']))
                pygame.draw.rect(self.screen, BLACK,
                               (screen_x, screen_y, building['width'], building['height']), 2)

        # Render landmarks
        for landmark in self.city.landmarks:
            screen_x = landmark['position'].x - self.camera.x
            screen_y = landmark['position'].y - self.camera.y

            if (-50 < screen_x < SCREEN_WIDTH + 50 and -50 < screen_y < SCREEN_HEIGHT + 50):
                # Landmark building
                size = landmark['size']
                pygame.draw.rect(self.screen, landmark['color'],
                               (screen_x - size//2, screen_y - size//2, size, size))
                pygame.draw.rect(self.screen, BLACK,
                               (screen_x - size//2, screen_y - size//2, size, size), 3)

                # Landmark name
                text = self.font_small.render(landmark['name'], True, WHITE)
                text_rect = text.get_rect(center=(screen_x, screen_y - size//2 - 15))
                self.screen.blit(text, text_rect)

    def render_passengers(self):
        """Render passengers waiting for pickup"""
        for passenger in self.passengers:
            if passenger.state == PassengerState.WAITING:
                screen_x = passenger.pickup_pos.x - self.camera.x
                screen_y = passenger.pickup_pos.y - self.camera.y

                if (0 < screen_x < SCREEN_WIDTH and 0 < screen_y < SCREEN_HEIGHT):
                    # Passenger icon
                    pygame.draw.circle(self.screen, BLUE, (int(screen_x), int(screen_y)), 8)
                    pygame.draw.circle(self.screen, WHITE, (int(screen_x), int(screen_y)), 8, 2)

                    # Patience indicator
                    patience_color = GREEN if passenger.patience > 60 else ORANGE if passenger.patience > 30 else RED
                    patience_width = int((passenger.patience / 100) * 20)
                    pygame.draw.rect(self.screen, RED, (screen_x - 10, screen_y - 15, 20, 4))
                    pygame.draw.rect(self.screen, patience_color, (screen_x - 10, screen_y - 15, patience_width, 4))

                    # Passenger name
                    name_text = self.font_small.render(passenger.name, True, WHITE)
                    name_rect = name_text.get_rect(center=(screen_x, screen_y + 20))
                    self.screen.blit(name_text, name_rect)

            # Show destination for active passenger
            if passenger == self.active_passenger:
                dest_x = passenger.destination.x - self.camera.x
                dest_y = passenger.destination.y - self.camera.y

                if (0 < dest_x < SCREEN_WIDTH and 0 < dest_y < SCREEN_HEIGHT):
                    # Destination marker
                    pygame.draw.circle(self.screen, RED, (int(dest_x), int(dest_y)), 12)
                    pygame.draw.circle(self.screen, WHITE, (int(dest_x), int(dest_y)), 12, 3)

                    # Destination text
                    dest_text = self.font_small.render("DESTINATION", True, WHITE)
                    dest_rect = dest_text.get_rect(center=(dest_x, dest_y + 25))
                    self.screen.blit(dest_text, dest_rect)

    def render_taxi(self):
        """Render the taxi"""
        screen_x = self.taxi.position.x - self.camera.x
        screen_y = self.taxi.position.y - self.camera.y

        # Taxi body
        taxi_points = [
            (-self.taxi.width//2, -self.taxi.height//2),
            (self.taxi.width//2, -self.taxi.height//2),
            (self.taxi.width//2, self.taxi.height//2),
            (-self.taxi.width//2, self.taxi.height//2)
        ]

        # Rotate points based on taxi angle
        rotated_points = []
        for px, py in taxi_points:
            rx = px * math.cos(self.taxi.angle) - py * math.sin(self.taxi.angle)
            ry = px * math.sin(self.taxi.angle) + py * math.cos(self.taxi.angle)
            rotated_points.append((screen_x + rx, screen_y + ry))

        # Draw taxi
        pygame.draw.polygon(self.screen, self.taxi.color, rotated_points)
        pygame.draw.polygon(self.screen, BLACK, rotated_points, 2)

        # Taxi roof light
        if self.active_passenger is None:
            light_color = GREEN  # Available
        else:
            light_color = RED    # Occupied

        pygame.draw.circle(self.screen, light_color, (int(screen_x), int(screen_y - 8)), 4)

        # Direction indicator
        front_x = screen_x + math.cos(self.taxi.angle) * self.taxi.width // 2
        front_y = screen_y + math.sin(self.taxi.angle) * self.taxi.width // 2
        pygame.draw.circle(self.screen, WHITE, (int(front_x), int(front_y)), 3)

    def render_ui(self):
        """Render game UI"""
        # UI background
        ui_height = 120
        pygame.draw.rect(self.screen, BLACK, (0, 0, SCREEN_WIDTH, ui_height))
        pygame.draw.rect(self.screen, WHITE, (0, 0, SCREEN_WIDTH, ui_height), 2)

        # Money and stats
        money_text = self.font_medium.render(f"💰 ${self.money:.2f}", True, GREEN)
        self.screen.blit(money_text, (10, 10))

        earnings_text = self.font_small.render(f"Total: ${self.total_earnings:.2f}", True, WHITE)
        self.screen.blit(earnings_text, (10, 35))

        trips_text = self.font_small.render(f"Trips: {self.trips_completed}", True, WHITE)
        self.screen.blit(trips_text, (10, 55))

        reputation_text = self.font_small.render(f"⭐ Rep: {self.reputation:.0f}%", True, YELLOW)
        self.screen.blit(reputation_text, (10, 75))

        # Fuel gauge
        fuel_x = 200
        fuel_y = 20
        fuel_width = 100
        fuel_height = 20

        pygame.draw.rect(self.screen, RED, (fuel_x, fuel_y, fuel_width, fuel_height))
        fuel_fill = int((self.taxi.fuel / 100) * fuel_width)
        fuel_color = GREEN if self.taxi.fuel > 50 else ORANGE if self.taxi.fuel > 20 else RED
        pygame.draw.rect(self.screen, fuel_color, (fuel_x, fuel_y, fuel_fill, fuel_height))
        pygame.draw.rect(self.screen, WHITE, (fuel_x, fuel_y, fuel_width, fuel_height), 2)

        fuel_text = self.font_small.render("⛽ Fuel", True, WHITE)
        self.screen.blit(fuel_text, (fuel_x, fuel_y - 15))

        fuel_percent = self.font_small.render(f"{self.taxi.fuel:.0f}%", True, WHITE)
        self.screen.blit(fuel_percent, (fuel_x + fuel_width + 5, fuel_y + 3))

        # Speed gauge
        speed_x = 350
        speed_y = 20
        speed_text = self.font_medium.render(f"🚗 {abs(self.taxi.speed):.0f} km/h", True, WHITE)
        self.screen.blit(speed_text, (speed_x, speed_y))

        # Taxi meter
        meter_x = 500
        meter_y = 20
        if self.taxi.meter_running:
            meter_color = GREEN
            meter_status = "ON"
        else:
            meter_color = RED
            meter_status = "OFF"

        meter_text = self.font_medium.render(f"📊 Meter: {meter_status}", True, meter_color)
        self.screen.blit(meter_text, (meter_x, meter_y))

        if self.active_passenger:
            fare_text = self.font_small.render(f"Fare: ${self.taxi.trip_distance * self.taxi.fare_rate:.2f}", True, WHITE)
            self.screen.blit(fare_text, (meter_x, meter_y + 25))

        # Time and weather
        time_x = SCREEN_WIDTH - 150
        time_y = 10

        hour = int(self.day_time)
        minute = int((self.day_time - hour) * 60)
        time_str = f"{hour:02d}:{minute:02d}"

        time_text = self.font_medium.render(f"🕐 {time_str}", True, WHITE)
        self.screen.blit(time_text, (time_x, time_y))

        # Current passenger info
        if self.active_passenger:
            passenger_info = f"👤 {self.active_passenger.name}"
            passenger_text = self.font_small.render(passenger_info, True, YELLOW)
            self.screen.blit(passenger_text, (time_x, time_y + 25))

            satisfaction = f"😊 {self.active_passenger.satisfaction:.0f}%"
            satisfaction_color = GREEN if self.active_passenger.satisfaction > 70 else ORANGE if self.active_passenger.satisfaction > 40 else RED
            satisfaction_text = self.font_small.render(satisfaction, True, satisfaction_color)
            self.screen.blit(satisfaction_text, (time_x, time_y + 45))

        # Minimap
        self.render_minimap()

    def render_minimap(self):
        """Render minimap"""
        minimap_size = 100
        minimap_x = SCREEN_WIDTH - minimap_size - 10
        minimap_y = SCREEN_HEIGHT - minimap_size - 10

        # Minimap background
        pygame.draw.rect(self.screen, BLACK, (minimap_x, minimap_y, minimap_size, minimap_size))
        pygame.draw.rect(self.screen, WHITE, (minimap_x, minimap_y, minimap_size, minimap_size), 2)

        # Scale factors
        scale_x = minimap_size / self.city.width
        scale_y = minimap_size / self.city.height

        # Draw taxi position
        taxi_mini_x = minimap_x + self.taxi.position.x * scale_x
        taxi_mini_y = minimap_y + self.taxi.position.y * scale_y
        pygame.draw.circle(self.screen, YELLOW, (int(taxi_mini_x), int(taxi_mini_y)), 3)

        # Draw passengers
        for passenger in self.passengers:
            if passenger.state == PassengerState.WAITING:
                pass_mini_x = minimap_x + passenger.pickup_pos.x * scale_x
                pass_mini_y = minimap_y + passenger.pickup_pos.y * scale_y
                pygame.draw.circle(self.screen, BLUE, (int(pass_mini_x), int(pass_mini_y)), 2)

        # Draw destination
        if self.active_passenger:
            dest_mini_x = minimap_x + self.active_passenger.destination.x * scale_x
            dest_mini_y = minimap_y + self.active_passenger.destination.y * scale_y
            pygame.draw.circle(self.screen, RED, (int(dest_mini_x), int(dest_mini_y)), 3)

        # Draw landmarks
        for landmark in self.city.landmarks:
            land_mini_x = minimap_x + landmark['position'].x * scale_x
            land_mini_y = minimap_y + landmark['position'].y * scale_y
            pygame.draw.circle(self.screen, landmark['color'], (int(land_mini_x), int(land_mini_y)), 2)

    def render_pause_overlay(self):
        """Render pause overlay"""
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        self.screen.blit(overlay, (0, 0))

        pause_text = self.font_large.render("PAUSED", True, WHITE)
        pause_rect = pause_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
        self.screen.blit(pause_text, pause_rect)

        resume_text = self.font_medium.render("Press ESC to resume", True, WHITE)
        resume_rect = resume_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 50))
        self.screen.blit(resume_text, resume_rect)

    def render_game_over(self):
        """Render game over screen"""
        self.screen.fill(BLACK)

        # Game Over title
        title = self.font_large.render("GAME OVER", True, RED)
        title_rect = title.get_rect(center=(SCREEN_WIDTH//2, 200))
        self.screen.blit(title, title_rect)

        # Final stats
        stats = [
            f"Total Earnings: ${self.total_earnings:.2f}",
            f"Trips Completed: {self.trips_completed}",
            f"Final Reputation: {self.reputation:.0f}%",
            "",
            "You ran out of fuel and money!",
            "",
            "Press ENTER to restart",
            "Press ESC to quit"
        ]

        y = 280
        for stat in stats:
            if stat:
                color = YELLOW if stat.startswith("Total") else WHITE
                text = self.font_medium.render(stat, True, color)
                text_rect = text.get_rect(center=(SCREEN_WIDTH//2, y))
                self.screen.blit(text, text_rect)
            y += 30

    def run(self):
        """Main game loop"""
        while self.running:
            dt = self.clock.tick(FPS) / 1000.0  # Delta time in seconds

            self.handle_events()

            if self.state == GameState.PLAYING:
                self.update(dt)

            self.render()

        pygame.quit()

if __name__ == "__main__":
    game = TaxiSimulator()
    game.run()
