# ✈️ Flight Simulator 3D

Một flight simulator 3D thực tế được viết bằng Python và Pygame với đồ họa 3D và physics chân thực.

## 🚀 Cài đặt

### Yêu cầu hệ thống
- Python 3.7 trở lên
- Windows, macOS, hoặc Linux

### Cài đặt dependencies
```bash
pip install -r requirements.txt
```

Hoặc cài đặt thủ công:
```bash
pip install pygame numpy
```

## 🎮 Chạy game

```bash
python main.py
```

## 🛩️ Điều khiển

### ✈️ Điều khiển bay
- **W/S**: Pitch (kéo mũi lên/xuống)
- **A/D**: Yaw (quay trái/phải)
- **Q/E**: Roll (nghiêng trái/phải)
- **Space**: Tăng ga
- **Shift**: Giảm ga

### 🔧 Điều khiển hệ thống
- **I**: B<PERSON>t/tắt động cơ
- **G**: <PERSON><PERSON><PERSON>/thu b<PERSON>h đáp
- **C**: <PERSON>y<PERSON><PERSON> đổi camera (Cockpit/Chase/External)
- **H**: B<PERSON>t/tắt HUD
- **P**: Tạm dừng
- **ESC**: Thoát game

## 📊 Tính năng

### 🛩️ Physics thực tế
- **6DOF Flight Model**: 6 bậc tự do (pitch, yaw, roll, x, y, z)
- **Realistic Aerodynamics**: Lực nâng, lực cản, trọng lực
- **Stall Mechanics**: Thất tốc khi bay quá chậm
- **Ground Effect**: Hiệu ứng mặt đất
- **Fuel System**: Hệ thống nhiên liệu thực tế

### 🎨 Đồ họa 3D
- **3D Terrain**: Địa hình 3D với núi đồi
- **3D Aircraft**: Mô hình máy bay 3D
- **Multiple Camera Modes**: 3 chế độ camera
- **Realistic Horizon**: Đường chân trời thực tế
- **Weather Effects**: Hiệu ứng thời tiết

### 🎛️ Cockpit chuyên nghiệp
- **Artificial Horizon**: Chân trời nhân tạo
- **Speed/Altitude Tapes**: Thước đo tốc độ và độ cao
- **Engine Gauges**: Đồng hồ động cơ
- **Compass**: La bàn điều hướng
- **Fuel Gauge**: Đồng hồ nhiên liệu

### 🗺️ Thế giới mở
- **Multiple Airports**: Nhiều sân bay
- **Procedural Terrain**: Địa hình tự động tạo
- **Obstacles**: Chướng ngại vật (núi, tòa nhà)
- **Weather System**: Hệ thống thời tiết

## 🎯 Cách chơi

### 🛫 Cất cánh
1. Nhấn **I** để khởi động động cơ
2. Nhấn **Space** để tăng ga
3. Khi đạt tốc độ 60+ km/h, nhấn **W** để kéo mũi lên
4. Máy bay sẽ cất cánh khi đạt đủ tốc độ

### ✈️ Bay
1. Sử dụng **WASD** để điều khiển hướng bay
2. Sử dụng **QE** để nghiêng máy bay
3. Theo dõi HUD để kiểm soát tốc độ và độ cao
4. Tránh va chạm với núi và tòa nhà

### 🛬 Hạ cánh
1. Giảm tốc độ xuống dưới 100 km/h
2. Hạ độ cao từ từ
3. Nhấn **G** để đưa bánh đáp
4. Tiếp đất nhẹ nhàng tại sân bay

## 📈 Mẹo chơi

### 🎯 Bay an toàn
- **Kiểm tra nhiên liệu** thường xuyên
- **Tránh bay quá chậm** (dưới 60 km/h)
- **Hạ cánh nhẹ nhàng** (dưới 100 km/h)
- **Theo dõi độ cao** để tránh va chạm

### 🎮 Sử dụng camera
- **Cockpit**: Trải nghiệm phi công thực thụ
- **Chase**: Theo dõi máy bay từ phía sau
- **External**: Quan sát tổng thể

### 📊 Đọc instruments
- **Speed Tape**: Tốc độ hiện tại (trái)
- **Altitude Tape**: Độ cao hiện tại (phải)
- **Artificial Horizon**: Tư thế máy bay
- **Compass**: Hướng bay

## 🔧 Tùy chỉnh

### ⚙️ Thay đổi cài đặt
Chỉnh sửa các constants trong `main.py`:
- `SCREEN_WIDTH/HEIGHT`: Độ phân giải
- `FPS`: Tốc độ khung hình
- Aircraft properties: Tính chất máy bay

### 🎨 Thêm tính năng
- Thêm loại máy bay mới
- Tạo sân bay mới
- Thêm hiệu ứng thời tiết
- Cải thiện đồ họa 3D

## 🐛 Troubleshooting

### ❌ Lỗi thường gặp
- **ImportError**: Cài đặt lại pygame và numpy
- **Màn hình đen**: Kiểm tra drivers đồ họa
- **Lag**: Giảm FPS hoặc độ phân giải

### 🔍 Debug
- Kiểm tra console để xem thông báo lỗi
- Đảm bảo Python 3.7+
- Cập nhật pygame lên phiên bản mới nhất

## 📝 Changelog

### v1.0.0
- ✅ 3D Flight physics
- ✅ Multiple camera modes
- ✅ Realistic cockpit
- ✅ Terrain generation
- ✅ Weather system
- ✅ HUD interface

## 🤝 Đóng góp

Hãy tạo pull request hoặc báo cáo bug qua Issues!

## 📄 License

MIT License - Sử dụng tự do cho mục đích học tập và phát triển.

---

**Chúc bạn có những chuyến bay an toàn! ✈️**
